# ElevenLabs Conversational AI Integration Setup

This document provides instructions for setting up the ElevenLabs Conversational AI integration in your fitness app's "Willow Voice" tab.

## 🎯 Prerequisites

1. **ElevenLabs Account**: Sign up at [elevenlabs.io](https://elevenlabs.io)
2. **Agent Created**: Your fitness coach agent "<PERSON>" is already configured with ID: `agent_01jx195padeb0spkjn54zt3ah0`
3. **API Key**: You need to obtain your ElevenLabs API key

## 🔑 Getting Your API Key

1. Go to [ElevenLabs API Settings](https://elevenlabs.io/app/settings/api-keys)
2. Sign in to your account
3. Click "Create API Key" or copy an existing one
4. Copy the API key (it starts with something like `sk-...`)

## ⚙️ Configuration

1. **Update Environment File**:
   - Open the `.env` file in your project root
   - Replace `your_api_key_here` with your actual ElevenLabs API key:
   ```
   ELEVENLABS_API_KEY=sk-your-actual-api-key-here
   ```

2. **Agent Configuration** (Already Done):
   - Agent ID: `agent_01jx195padeb0spkjn54zt3ah0`
   - Agent Name: Nathan - Fitness Coach
   - Configured for fitness coaching context

## 🚀 Features Implemented

### Real-time Voice Conversation
- **WebSocket Connection**: Direct connection to ElevenLabs Conversational AI
- **Voice Activity Detection**: Real-time detection of user speech
- **Streaming Audio**: Low-latency audio streaming for natural conversations

### Enhanced UI/UX
- **Dynamic Voice Visualizer**: Visual feedback based on conversation state
- **Connection Status**: Clear indicators for connection state
- **Error Handling**: Comprehensive error handling with user feedback

### Conversation Modes
- **Listening**: Red visualization when recording user input
- **Speaking**: Green visualization when Nathan is responding
- **Processing**: Blue visualization during AI processing
- **Idle**: Orange visualization when ready for input

## 🎨 Voice Visualizer States

The voice visualizer changes based on the conversation state:

- **🔴 Listening Mode**: Red pulsing rings with VAD-based intensity
- **🟢 Speaking Mode**: Green wave-like animations when Nathan talks
- **🔵 Processing Mode**: Blue steady glow during AI processing
- **🟠 Idle Mode**: Orange gentle pulse when ready

## 📱 Usage Instructions

1. **Start Conversation**: The app automatically connects to Nathan when you open the Voice tab
2. **Talk to Nathan**: Tap and hold the microphone button to speak
3. **Listen to Responses**: Nathan will respond with voice and text
4. **View Chat History**: All conversations are displayed in the chat interface
5. **Send Text**: Use the keyboard button to send text messages for testing

## 🔧 Technical Implementation

### Service Architecture
- **ElevenLabsConversationalService**: Main service handling WebSocket connections
- **Conversation Models**: Type-safe models for all WebSocket messages
- **Stream-based Communication**: Reactive streams for real-time updates

### Audio Configuration
- **Sample Rate**: 16kHz (optimized for speech recognition)
- **Channels**: Mono (1 channel)
- **Format**: WAV encoding
- **Bit Rate**: 128kbps

### Security
- **API Key Protection**: Environment variables prevent key exposure
- **Secure WebSocket**: WSS connection for encrypted communication
- **Permission Handling**: Proper microphone permission management

## 🐛 Troubleshooting

### Common Issues

1. **"Failed to initialize" Error**:
   - Check your API key in the `.env` file
   - Ensure you have an active ElevenLabs subscription
   - Verify internet connection

2. **"Microphone permission required" Error**:
   - Grant microphone permissions in device settings
   - Restart the app after granting permissions

3. **"Connection error" Message**:
   - Check internet connectivity
   - Verify the agent ID is correct
   - Ensure ElevenLabs service is operational

4. **No Audio Playback**:
   - Check device volume settings
   - Ensure audio permissions are granted
   - Try using headphones to test audio output

### Debug Information

The app logs detailed information to the console:
- 🎤 Voice recording events
- 📤 WebSocket message sending
- 📥 WebSocket message receiving
- 🤖 AI response processing
- 🔊 Audio playback events

## 📊 Monitoring and Analytics

The integration supports ElevenLabs' built-in analytics:
- Conversation duration tracking
- Response quality metrics
- Usage statistics
- Error rate monitoring

Access these through your ElevenLabs dashboard.

## 🔄 Updates and Maintenance

### Regular Tasks
1. **Monitor API Usage**: Check your ElevenLabs usage dashboard
2. **Update Agent Configuration**: Refine Nathan's prompts based on user feedback
3. **Review Conversation Logs**: Analyze conversations for improvement opportunities

### Version Updates
- Keep the ElevenLabs service updated for new features
- Monitor Flutter dependencies for security updates
- Test voice functionality after app updates

## 📞 Support

For technical issues:
1. **ElevenLabs Support**: [help.elevenlabs.io](https://help.elevenlabs.io)
2. **API Documentation**: [elevenlabs.io/docs](https://elevenlabs.io/docs)
3. **Community Discord**: [discord.gg/elevenlabs](https://discord.gg/elevenlabs)

## 🎉 Success!

Once configured, your users can:
- Have natural voice conversations with Nathan
- Get real-time fitness coaching and motivation
- Receive personalized workout guidance
- Experience seamless voice interaction during workouts

The "Willow Voice" tab is now powered by ElevenLabs Conversational AI! 🚀
