# iOS Microphone Permission Fix

## Steps to Fix Microphone Permission on iOS

### 1. Clean Build
First, clean your build to ensure all changes are properly applied:

```bash
cd ios
rm -rf Pods
rm -rf ~/Library/Developer/Xcode/DerivedData/*
pod cache clean --all
```

### 2. Reinstall Pods
After updating the Podfile with permission configurations:

```bash
pod install
```

### 3. Clean Flutter Build
```bash
cd ..
flutter clean
flutter pub get
```

### 4. Reset Simulator/Device Permissions (if testing on simulator)
- For Simulator: Device > Erase All Content and Settings
- For Physical Device: Settings > General > Transfer or Reset iPhone > Reset > Reset Location & Privacy

### 5. Build and Run
```bash
flutter run --release
```

## What Was Changed

1. **Podfile**: Added specific permission_handler configuration with `PERMISSION_MICROPHONE=1` preprocessor definition
2. **Info.plist**: Already had NSMicrophoneUsageDescription, added UIBackgroundModes for audio
3. **ElevenLabsConversationalService**: Enhanced permission handling with better iOS status checks
4. **VoiceChatScreen**: Added ability to open app settings directly when permission is denied

## Important Notes

- The app MUST be completely uninstalled and reinstalled on the device for permission changes to take effect
- iOS will only show the permission dialog ONCE per app install
- If previously denied, the user must go to Settings > Privacy & Security > Microphone > Fitness App
- The bundle identifier is: com.abenezernuro.agenticfit

## Troubleshooting

If permission still shows as "permanently denied":
1. Uninstall the app completely from the device
2. Restart the device
3. Clean build and reinstall
4. When the permission dialog appears, tap "Allow"

If the dialog never appears:
1. Check that the bundle identifier matches in Xcode project settings
2. Ensure the device hasn't disabled all microphone access globally
3. Try on a different device or reset privacy settings