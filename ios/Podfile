# Uncomment this line to define a global platform for your project
platform :ios, '13.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  
  # Permission handler configuration
  # This is REQUIRED for permission_handler to work properly on iOS
  # Add ONLY the permissions your app actually needs
  
  # Preprocessor definitions for permission_handler
  # IMPORTANT: Only include the permissions you need to avoid App Store rejection
  pod 'permission_handler_apple', :path => File.join('.symlinks', 'plugins', 'permission_handler_apple', 'ios'), 
    :modular_headers => true,
    :inhibit_warnings => true
  
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    
    # Required for permission_handler
    target.build_configurations.each do |config|
      # You can enable the permissions needed by your app here, by listing each 
      # permission you need as a string
      
      # Microphone permission for voice chat
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        
        # Add permissions here. For example:
        'PERMISSION_MICROPHONE=1',
        
        # Other permissions you might need (commented out if not using):
        # 'PERMISSION_CAMERA=1',
        # 'PERMISSION_PHOTOS=1',
        # 'PERMISSION_LOCATION=1',
        # 'PERMISSION_NOTIFICATIONS=1',
        # 'PERMISSION_MEDIA_LIBRARY=1',
        # 'PERMISSION_SENSORS_MOTION=1',
        # 'PERMISSION_BLUETOOTH=1',
        # 'PERMISSION_APP_TRACKING_TRANSPARENCY=1',
        # 'PERMISSION_CONTACTS=1',
        # 'PERMISSION_CALENDAR=1',
        # 'PERMISSION_REMINDERS=1',
        # 'PERMISSION_SPEECH_RECOGNIZER=1',
      ]
    end
  end
end
