{"name": "Testing chat flow 6/3/2025", "nodes": [{"parameters": {"public": true, "mode": "webhook", "options": {"loadPreviousSession": "memory", "responseMode": "responseNode"}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-700, -360], "id": "8f25ca8c-2179-484c-96a3-e4e76fd6842c", "name": "When chat message received", "webhookId": "411c6d55-11b0-4193-8506-a5a66d7fccea"}, {"parameters": {"options": {"systemMessage": "=act as a workout coach"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [60, -380], "id": "b43a2566-0e55-41b5-a0ff-29920af353f0", "name": "Chat Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [80, -180], "id": "49b36519-44a1-4ce8-bfff-3a46a7c766a1", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "ZXFXTfTP6H03B3vz", "name": "OpenAi account"}}}, {"parameters": {"contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-680, -180], "id": "********-5b1d-4e76-80d0-c0bada4e8807", "name": "Simple Memory"}], "pinData": {"When chat message received": [{"json": {"sessionId": "5769f401c2b14a7b8b740427144e56fb", "action": "sendMessage", "chatInput": "what is my next workout?", "user_id": "4d8a8d40-def8-4179-a4cd-ec3eebbbdeb8"}}]}, "connections": {"When chat message received": {"main": [[{"node": "Chat Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Chat Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "When chat message received", "type": "ai_memory", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "c8e5980e-b724-4062-9c62-469e88188dbb", "meta": {"instanceId": "6195c139c872709f5410ba81a9d6cc1e5c549703764aa55ff6b328be53cf73c2"}, "id": "vqkkcEQI5pfTO9FD", "tags": []}