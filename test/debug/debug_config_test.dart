import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fitness_app/core/debug/debug_config.dart';

void main() {
  group('DebugConfig Tests', () {
    testWidgets('safeText should handle overflow gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 50, // Very narrow width to force overflow
              child: DebugConfig.safeText(
                'This is a very long text that should overflow',
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ),
        ),
      );

      // Verify the text widget exists
      expect(find.byType(Text), findsOneWidget);
      
      // Verify no overflow errors occurred
      expect(tester.takeException(), isNull);
    });

    testWidgets('safeColumn should handle overflow gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              height: 100, // Limited height to force overflow
              child: DebugConfig.safeColumn(
                children: List.generate(
                  10,
                  (index) => Container(
                    height: 50,
                    color: Colors.blue,
                    child: Text('Item $index'),
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      // Verify the column exists
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify no overflow errors occurred
      expect(tester.takeException(), isNull);
    });

    testWidgets('safeRow should handle overflow gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 100, // Limited width to force overflow
              child: DebugConfig.safeRow(
                children: List.generate(
                  10,
                  (index) => Container(
                    width: 50,
                    color: Colors.red,
                    child: Text('$index'),
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      // Verify the row exists
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify no overflow errors occurred
      expect(tester.takeException(), isNull);
    });

    testWidgets('wrapWithOverflowProtection should prevent overflow', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 50,
              height: 50,
              child: DebugConfig.wrapWithOverflowProtection(
                Container(
                  width: 200,
                  height: 200,
                  color: Colors.green,
                  child: const Text('Large content'),
                ),
              ),
            ),
          ),
        ),
      );

      // Verify the overflow box exists
      expect(find.byType(OverflowBox), findsOneWidget);
      
      // Verify no overflow errors occurred
      expect(tester.takeException(), isNull);
    });

    test('isOverflowError should correctly identify overflow errors', () {
      // Test overflow error detection
      final overflowError = FlutterErrorDetails(
        exception: Exception('RenderFlex overflowed by 34 pixels'),
        library: 'test',
        stack: StackTrace.current,
      );

      final normalError = FlutterErrorDetails(
        exception: Exception('Some other error'),
        library: 'test',
        stack: StackTrace.current,
      );

      // This is testing a private method, so we'll test the public behavior
      // by checking if the error widget builder handles overflow errors correctly
      final overflowWidget = DebugConfig.errorWidgetBuilder(overflowError);
      final normalWidget = DebugConfig.errorWidgetBuilder(normalError);

      expect(overflowWidget, isA<SizedBox>());
      expect(normalWidget, isA<Container>());
    });
  });
}
