import 'dart:convert';

class ValidationUtils {
  // UUID validation for user IDs
  static bool isValidUUID(String? value) {
    if (value == null || value.isEmpty) return false;
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    );
    return uuidRegex.hasMatch(value);
  }

  // Sanitize string input
  static String sanitizeString(String? input, {int maxLength = 255}) {
    if (input == null) return '';
    // Remove potentially harmful characters and limit length
    final sanitized = input
        .replaceAll('<', '')
        .replaceAll('>', '')
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll('`', '')
        .replaceAll(RegExp(r'[^\w\s\-.,!?()]'), '')
        .trim();
    return sanitized.length > maxLength 
        ? sanitized.substring(0, maxLength) 
        : sanitized;
  }

  // Validate numeric ranges
  static bool isValidReps(int? reps) {
    return reps != null && reps >= 0 && reps <= 100;
  }

  static bool isValidWeight(double? weight) {
    return weight != null && weight >= 0 && weight <= 1000; // Max 1000 lbs/kg
  }

  static bool isValidDuration(int? minutes) {
    return minutes != null && minutes >= 0 && minutes <= 720; // Max 12 hours
  }

  static bool isValidRating(int? rating) {
    return rating != null && rating >= 1 && rating <= 5;
  }

  // Validate workout status
  static bool isValidWorkoutStatus(String? status) {
    const validStatuses = ['not_started', 'in_progress', 'completed', 'paused', 'cancelled'];
    return status != null && validStatuses.contains(status);
  }

  // Validate difficulty level
  static bool isValidDifficulty(String? difficulty) {
    const validDifficulties = ['Beginner', 'Intermediate', 'Advanced'];
    return difficulty != null && validDifficulties.contains(difficulty);
  }

  // Validate fitness level (numeric)
  static bool isValidFitnessLevelNumber(int? level) {
    return level != null && level >= 1 && level <= 5;
  }

  // Validate fitness goals
  static bool isValidFitnessGoal(String? goal) {
    const validGoals = [
      'Weight Loss', 'Muscle Gain', 'Endurance', 'Strength', 
      'Flexibility', 'General Fitness', 'Sport Performance'
    ];
    return goal != null && validGoals.contains(goal);
  }

  // Validate JSON data size and structure
  static bool isValidJsonData(Map<String, dynamic>? data, {int maxSizeBytes = 10240}) {
    if (data == null) return true;
    try {
      final jsonString = jsonEncode(data);
      return jsonString.length <= maxSizeBytes;
    } catch (e) {
      return false;
    }
  }

  // Validate email format
  static bool isValidEmail(String? email) {
    if (email == null || email.isEmpty) return false;
    final emailRegex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
    return emailRegex.hasMatch(email);
  }

  // Validate exercise name
  static bool isValidExerciseName(String? name) {
    if (name == null || name.isEmpty) return false;
    return name.length >= 2 && name.length <= 100;
  }

  // Validate positive integer
  static bool isPositiveInteger(int? value) {
    return value != null && value > 0;
  }

  // Validate date string (ISO format)
  static bool isValidISODate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return false;
    try {
      DateTime.parse(dateString);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Clean and validate user feedback
  static String sanitizeFeedback(String? feedback) {
    if (feedback == null) return '';
    // Remove scripts, HTML tags, and limit length
    final cleaned = feedback
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .replaceAll('<', '')
        .replaceAll('>', '')
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll('`', '')
        .trim();
    return cleaned.length > 500 ? cleaned.substring(0, 500) : cleaned;
  }
}