import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Debug configuration utilities for the fitness app
class DebugConfig {
  /// Initialize debug settings for the app
  static void initialize() {
    if (kDebugMode) {
      _configureDebugSettings();
      _configureErrorHandling();
    }
  }

  /// Configure debug-specific settings
  static void _configureDebugSettings() {
    // Disable debug paint size indicators
    debugPaintSizeEnabled = false;
    
    // Disable debug repaint rainbow
    debugRepaintRainbowEnabled = false;
    
    // Configure debug print behavior
    debugPrint = (String? message, {int? wrapWidth}) {
      // Only print non-overflow related debug messages
      if (message != null && !_isOverflowMessage(message)) {
        debugPrintThrottled(message, wrapWidth: wrapWidth);
      }
    };
  }

  /// Configure error handling to suppress overflow indicators
  static void _configureErrorHandling() {
    FlutterError.onError = (FlutterErrorDetails details) {
      // Check if this is an overflow error
      if (_isOverflowError(details)) {
        // Silently ignore overflow errors in debug mode
        return;
      }
      
      // For other errors, use the default error handling
      FlutterError.presentError(details);
    };
  }

  /// Check if an error is related to overflow
  static bool _isOverflowError(FlutterErrorDetails details) {
    final errorString = details.exception.toString().toLowerCase();
    return errorString.contains('renderbox overflowed') ||
           errorString.contains('renderflex overflowed') ||
           errorString.contains('overflowed by') ||
           errorString.contains('pixels');
  }

  /// Check if a debug message is related to overflow
  static bool _isOverflowMessage(String message) {
    final lowerMessage = message.toLowerCase();
    return lowerMessage.contains('overflowed') ||
           lowerMessage.contains('overflow') ||
           lowerMessage.contains('pixels');
  }

  /// Custom error widget builder that hides overflow indicators
  static Widget errorWidgetBuilder(FlutterErrorDetails errorDetails) {
    if (_isOverflowError(errorDetails)) {
      // Return an invisible widget for overflow errors
      return const SizedBox.shrink();
    }
    
    // For other errors, show a minimal error indicator
    return Container(
      color: Colors.red.withOpacity(0.1),
      child: const Center(
        child: Icon(
          Icons.error_outline,
          color: Colors.red,
          size: 24,
        ),
      ),
    );
  }

  /// Widget wrapper that prevents overflow indicators
  static Widget wrapWithOverflowProtection(Widget child) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return OverflowBox(
          maxWidth: constraints.maxWidth,
          maxHeight: constraints.maxHeight,
          child: child,
        );
      },
    );
  }

  /// Safe text widget that prevents overflow
  static Widget safeText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines ?? 1,
      overflow: overflow ?? TextOverflow.ellipsis,
      softWrap: true,
    );
  }

  /// Safe column that prevents overflow
  static Widget safeColumn({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisAlignment: mainAxisAlignment,
                crossAxisAlignment: crossAxisAlignment,
                mainAxisSize: mainAxisSize,
                children: children,
              ),
            ),
          ),
        );
      },
    );
  }

  /// Safe row that prevents overflow
  static Widget safeRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: constraints.maxWidth,
            ),
            child: IntrinsicWidth(
              child: Row(
                mainAxisAlignment: mainAxisAlignment,
                crossAxisAlignment: crossAxisAlignment,
                mainAxisSize: mainAxisSize,
                children: children,
              ),
            ),
          ),
        );
      },
    );
  }

  /// Check if we're in debug mode
  static bool get isDebugMode => kDebugMode;

  /// Check if we're in release mode
  static bool get isReleaseMode => kReleaseMode;

  /// Check if we're in profile mode
  static bool get isProfileMode => kProfileMode;
}
