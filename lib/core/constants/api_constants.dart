/// API constants for ElevenLabs Conversational AI integration
class ApiConstants {
  // ElevenLabs Conversational AI endpoints
  static const String elevenLabsBaseUrl = 'api.elevenlabs.io';
  static const String conversationWebSocketPath = '/v1/convai/conversation';
  static const String signedUrlPath = '/v1/convai/conversation/get-signed-url';
  
  // WebSocket configuration
  static const String webSocketScheme = 'wss';
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration heartbeatInterval = Duration(seconds: 30);
  
  // Audio configuration
  static const int sampleRate = 16000;
  static const int channels = 1;
  static const int bitRate = 128000;
  
  // Conversation settings
  static const Duration maxSilenceDuration = Duration(seconds: 10);
  static const Duration responseTimeout = Duration(seconds: 30);
  
  // Error messages
  static const String connectionErrorMessage = 'Failed to connect to voice assistant';
  static const String microphonePermissionError = 'Microphone permission required';
  static const String networkErrorMessage = 'Network connection error';
  static const String authenticationErrorMessage = 'Authentication failed';
}
