/// Base class for all application exceptions
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const AppException(this.message, {this.code, this.originalError});

  @override
  String toString() => 'AppException: $message';
}

/// Authentication related exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'AuthException: $message';
}

/// Database operation exceptions
class DatabaseException extends AppException {
  const DatabaseException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'DatabaseException: $message';
}

/// Validation exceptions
class ValidationException extends AppException {
  final String field;

  const ValidationException(super.message, this.field, {super.code, super.originalError});

  @override
  String toString() => 'ValidationException: $field - $message';
}

/// Network related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'NetworkException: $message';
}

/// User not found exceptions
class UserNotFoundException extends AppException {
  const UserNotFoundException([super.message = 'User not found']);

  @override
  String toString() => 'UserNotFoundException: $message';
}

/// Workout related exceptions
class WorkoutException extends AppException {
  const WorkoutException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'WorkoutException: $message';
}

/// Service unavailable exceptions
class ServiceUnavailableException extends AppException {
  const ServiceUnavailableException([super.message = 'Service temporarily unavailable']);

  @override
  String toString() => 'ServiceUnavailableException: $message';
}

/// Permission denied exceptions
class PermissionDeniedException extends AppException {
  const PermissionDeniedException([super.message = 'Permission denied']);

  @override
  String toString() => 'PermissionDeniedException: $message';
}