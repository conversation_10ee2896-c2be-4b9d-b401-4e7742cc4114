import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'app_exceptions.dart' as app_exceptions;

/// Centralized error handling utility
class ErrorHandler {
  /// Handle database errors and convert to appropriate app exceptions
  static app_exceptions.AppException handleDatabaseError(dynamic error, {String? operation}) {
    final operationContext = operation != null ? ' during $operation' : '';
    
    if (error is PostgrestException) {
      switch (error.code) {
        case '23505': // Unique constraint violation
          return app_exceptions.DatabaseException('Duplicate data detected$operationContext', 
              code: error.code, originalError: error);
        case '23503': // Foreign key constraint violation
          return app_exceptions.DatabaseException('Referenced data not found$operationContext', 
              code: error.code, originalError: error);
        case '42501': // Insufficient privileges
          return app_exceptions.PermissionDeniedException('Access denied$operationContext');
        default:
          return app_exceptions.DatabaseException('Database operation failed$operationContext: ${error.message}', 
              code: error.code, originalError: error);
      }
    }
    
    if (error is AuthException) {
      return app_exceptions.AuthException('Authentication failed$operationContext: ${error.message}', 
          originalError: error);
    }
    
    return app_exceptions.DatabaseException('Unexpected database error$operationContext: ${error.toString()}', 
        originalError: error);
  }

  /// Handle authentication errors
  static app_exceptions.AppException handleAuthError(dynamic error) {
    if (error is AuthException) {
      switch (error.message) {
        case 'Invalid login credentials':
          return const app_exceptions.AuthException('Invalid email or password');
        case 'Email not confirmed':
          return const app_exceptions.AuthException('Please verify your email address');
        case 'User not found':
          return const app_exceptions.UserNotFoundException();
        default:
          return app_exceptions.AuthException('Authentication failed: ${error.message}', originalError: error);
      }
    }
    
    return app_exceptions.AuthException('Authentication error: ${error.toString()}', originalError: error);
  }

  /// Handle network errors
  static app_exceptions.AppException handleNetworkError(dynamic error) {
    return app_exceptions.NetworkException('Network error: ${error.toString()}', originalError: error);
  }

  /// Handle validation errors
  static app_exceptions.ValidationException handleValidationError(String field, String message) {
    return app_exceptions.ValidationException(message, field);
  }

  /// Log error with consistent format
  static void logError(app_exceptions.AppException error, {String? context, StackTrace? stackTrace}) {
    if (kDebugMode) {
      final contextInfo = context != null ? '[$context] ' : '';
      debugPrint('🚨 ${contextInfo}${error.toString()}');
      if (error.originalError != null) {
        debugPrint('📄 Original error: ${error.originalError}');
      }
      if (stackTrace != null) {
        debugPrint('📚 Stack trace: $stackTrace');
      }
    }
  }

  /// Get user-friendly error message for UI display
  static String getUserFriendlyMessage(app_exceptions.AppException error) {
    switch (error.runtimeType) {
      case app_exceptions.AuthException:
        return error.message;
      case app_exceptions.NetworkException:
        return 'Network connection error. Please check your internet connection.';
      case app_exceptions.DatabaseException:
        return 'Unable to load data. Please try again.';
      case app_exceptions.ValidationException:
        return error.message;
      case app_exceptions.UserNotFoundException:
        return 'User account not found.';
      case app_exceptions.WorkoutException:
        return 'Workout error: ${error.message}';
      case app_exceptions.ServiceUnavailableException:
        return 'Service temporarily unavailable. Please try again later.';
      case app_exceptions.PermissionDeniedException:
        return 'You don\'t have permission to perform this action.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Check if error should trigger retry
  static bool shouldRetry(app_exceptions.AppException error) {
    switch (error.runtimeType) {
      case app_exceptions.NetworkException:
      case app_exceptions.ServiceUnavailableException:
        return true;
      case app_exceptions.DatabaseException:
        // Only retry for transient database errors
        return error.code == null || !['23505', '23503', '42501'].contains(error.code);
      default:
        return false;
    }
  }
}