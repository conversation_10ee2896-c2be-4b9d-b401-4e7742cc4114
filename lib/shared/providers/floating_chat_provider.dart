import 'package:flutter_riverpod/flutter_riverpod.dart';

enum FloatingChatMode { minimized, expanded, fullscreen }

class FloatingChatState {
  final FloatingChatMode mode;
  final bool isVisible;

  const FloatingChatState({
    this.mode = FloatingChatMode.minimized,
    this.isVisible = true,
  });

  FloatingChatState copyWith({
    FloatingChatMode? mode,
    bool? isVisible,
  }) {
    return FloatingChatState(
      mode: mode ?? this.mode,
      isVisible: isVisible ?? this.isVisible,
    );
  }
}

class FloatingChatNotifier extends StateNotifier<FloatingChatState> {
  FloatingChatNotifier() : super(const FloatingChatState());

  void toggleChat() {
    if (state.mode == FloatingChatMode.minimized) {
      state = state.copyWith(mode: FloatingChatMode.expanded);
    } else {
      state = state.copyWith(mode: FloatingChatMode.minimized);
    }
  }

  void toggleFullscreen() {
    if (state.mode == FloatingChatMode.fullscreen) {
      state = state.copyWith(mode: FloatingChatMode.expanded);
    } else {
      state = state.copyWith(mode: FloatingChatMode.fullscreen);
    }
  }

  void minimize() {
    state = state.copyWith(mode: FloatingChatMode.minimized);
  }

  void expand() {
    state = state.copyWith(mode: FloatingChatMode.expanded);
  }

  void fullscreen() {
    state = state.copyWith(mode: FloatingChatMode.fullscreen);
  }

  void hide() {
    state = state.copyWith(isVisible: false);
  }

  void show() {
    state = state.copyWith(isVisible: true);
  }
}

final floatingChatProvider = StateNotifierProvider<FloatingChatNotifier, FloatingChatState>((ref) {
  return FloatingChatNotifier();
}); 