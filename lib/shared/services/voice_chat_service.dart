import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

class VoiceChatService {
  static final AudioRecorder _audioRecorder = AudioRecorder();
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static bool _isRecording = false;
  static bool _isInitialized = false;
  static String? _currentRecordingPath;
  
  // Stream controllers for real-time communication
  static final StreamController<String> _transcriptionController = 
      StreamController<String>.broadcast();
  static final StreamController<String> _responseController = 
      StreamController<String>.broadcast();
  static final StreamController<bool> _recordingStatusController = 
      StreamController<bool>.broadcast();

  // Public streams
  static Stream<String> get transcriptionStream => _transcriptionController.stream;
  static Stream<String> get responseStream => _responseController.stream;
  static Stream<bool> get recordingStatusStream => _recordingStatusController.stream;

  // Initialize the service
  static Future<void> initialize() async {
    try {
      // Request microphone permission
      final micPermission = await Permission.microphone.request();
      if (micPermission != PermissionStatus.granted) {
        throw Exception('Microphone permission required for voice chat');
      }

      _isInitialized = true;
      
      print('🎤 Voice chat service initialized');
    } catch (e) {
      print('❌ Failed to initialize voice chat service: $e');
      throw Exception('Failed to initialize voice chat: $e');
    }
  }

  // Start recording audio
  static Future<void> startRecording() async {
    if (!_isInitialized) {
      throw Exception('Voice chat service not initialized');
    }

    if (_isRecording) return;

    try {
      // Get temporary directory for recording
      final tempDir = await getTemporaryDirectory();
      _currentRecordingPath = '${tempDir.path}/voice_recording_${DateTime.now().millisecondsSinceEpoch}.wav';

      // Start recording with high-quality settings for speech recognition
      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.wav,
          sampleRate: 16000,
          numChannels: 1,
          bitRate: 128000,
        ),
        path: _currentRecordingPath!,
      );

      _isRecording = true;
      _recordingStatusController.add(true);
      print('🎤 Started voice recording');
    } catch (e) {
      print('❌ Failed to start recording: $e');
      throw Exception('Failed to start recording: $e');
    }
  }

  // Stop recording and process audio
  static Future<void> stopRecording() async {
    if (!_isRecording) return;

    try {
      // Stop recording
      final recordingPath = await _audioRecorder.stop();
      _isRecording = false;
      _recordingStatusController.add(false);

      if (recordingPath != null) {
        print('🎤 Recording stopped, processing audio...');
        await _processAudioFile(recordingPath);
      }
    } catch (e) {
      print('❌ Failed to stop recording: $e');
      _isRecording = false;
      _recordingStatusController.add(false);
    }
  }

  // Process the recorded audio file
  static Future<void> _processAudioFile(String audioPath) async {
    try {
      // Read the audio file
      final audioFile = File(audioPath);
      final audioBytes = await audioFile.readAsBytes();

      // For demo purposes, simulate transcription
      // TODO: Integrate with Google Speech-to-Text or similar service
      final simulatedTranscription = await _simulateTranscription(audioBytes);
      _transcriptionController.add(simulatedTranscription);

      // Send transcription to AI for response
      await _getAIResponse(simulatedTranscription);

      // Clean up the temporary file
      if (await audioFile.exists()) {
        await audioFile.delete();
      }
    } catch (e) {
      print('❌ Failed to process audio: $e');
    }
  }

  // Simulate speech-to-text transcription (replace with real STT in production)
  static Future<String> _simulateTranscription(Uint8List audioBytes) async {
    // Simulate processing delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Return a simulated transcription based on audio length
    final audioLength = audioBytes.length;
    if (audioLength < 10000) {
      return "Hi Nathan!";
    } else if (audioLength < 30000) {
      return "What's my workout for today?";
    } else if (audioLength < 50000) {
      return "Can you help me with my fitness goals?";
    } else {
      return "I need some motivation for my workout routine.";
    }
  }

  // Get AI response (simplified for demo)
  static Future<void> _getAIResponse(String message) async {
    try {
      print('🤖 Processing: $message');
      
      // Simulate AI processing delay
      await Future.delayed(const Duration(milliseconds: 1000));
      
      // Generate contextual responses based on the input
      String responseText;
      final lowerMessage = message.toLowerCase();
      
      if (lowerMessage.contains('hi') || lowerMessage.contains('hello')) {
        responseText = "Hey there! I'm Nathan, your AI fitness coach! Ready to crush your goals today?";
      } else if (lowerMessage.contains('workout')) {
        responseText = "Let's get that workout started! I've got the perfect routine designed just for you. You've got this!";
      } else if (lowerMessage.contains('motivation') || lowerMessage.contains('help')) {
        responseText = "You're stronger than you think! Every rep, every set, every drop of sweat is building a better you. Let's do this!";
      } else if (lowerMessage.contains('goals') || lowerMessage.contains('fitness')) {
        responseText = "Your fitness goals are within reach! I'm here to guide you every step of the way. What's your biggest challenge right now?";
      } else {
        responseText = "I'm here to support your fitness journey! Whether it's workouts, nutrition, or motivation - let's achieve greatness together!";
      }
      
      print('🤖 AI Response: $responseText');
      _responseController.add(responseText);
      
      // Convert response to speech and play
      await _textToSpeech(responseText);
    } catch (e) {
      print('❌ Failed to get AI response: $e');
      const errorMessage = 'Sorry, I had trouble processing that. Could you try again?';
      _responseController.add(errorMessage);
      await _textToSpeech(errorMessage);
    }
  }

  // Convert text to speech and play (simplified for demo)
  static Future<void> _textToSpeech(String text) async {
    try {
      // For demo purposes, we'll just log the TTS
      // TODO: Integrate with platform TTS or cloud TTS service
      print('🔊 TTS: $text');
      
      // Simulate TTS processing time
      await Future.delayed(Duration(milliseconds: text.length * 30));
      
      // In a real implementation, you would:
      // 1. Send text to TTS API (Google Cloud TTS, Azure Speech, etc.)
      // 2. Get audio bytes back
      // 3. Play audio using AudioPlayer
      
      // For now, just indicate TTS is "playing"
      print('🔊 Playing TTS response');
    } catch (e) {
      print('❌ TTS failed: $e');
    }
  }

  // Send text message directly (for testing)
  static Future<void> sendTextMessage(String message) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    _transcriptionController.add(message);
    await _getAIResponse(message);
  }

  // Clear chat history
  static Future<void> clearChat() async {
    print('🧹 Chat history cleared');
  }

  // Check if currently recording
  static bool get isRecording => _isRecording;
  static bool get isInitialized => _isInitialized;

  // Dispose resources
  static Future<void> dispose() async {
    await _audioRecorder.dispose();
    await _audioPlayer.dispose();
    await _transcriptionController.close();
    await _responseController.close();
    await _recordingStatusController.close();
    _isInitialized = false;
  }
} 