import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import '../models/conversation_models.dart';
import '../../core/constants/api_constants.dart';

/// Service for managing ElevenLabs Conversational AI integration
class ElevenLabsConversationalService {
  static ElevenLabsConversationalService? _instance;
  static ElevenLabsConversationalService get instance {
    _instance ??= ElevenLabsConversationalService._();
    return _instance!;
  }
  
  ElevenLabsConversationalService._();
  
  // WebSocket connection
  WebSocketChannel? _webSocketChannel;
  
  // Audio components
  final AudioRecorder _audioRecorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // State management
  bool _isInitialized = false;
  bool _isConnected = false;
  bool _isRecording = false;
  String? _conversationId;
  String? _currentRecordingPath;
  
  // Stream controllers
  final StreamController<ConversationStatus> _statusController = 
      StreamController<ConversationStatus>.broadcast();
  final StreamController<ConversationMode> _modeController = 
      StreamController<ConversationMode>.broadcast();
  final StreamController<String> _transcriptionController = 
      StreamController<String>.broadcast();
  final StreamController<String> _responseController = 
      StreamController<String>.broadcast();
  final StreamController<bool> _recordingStatusController = 
      StreamController<bool>.broadcast();
  final StreamController<double> _vadScoreController = 
      StreamController<double>.broadcast();
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  
  // Public streams
  Stream<ConversationStatus> get statusStream => _statusController.stream;
  Stream<ConversationMode> get modeStream => _modeController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<String> get responseStream => _responseController.stream;
  Stream<bool> get recordingStatusStream => _recordingStatusController.stream;
  Stream<double> get vadScoreStream => _vadScoreController.stream;
  Stream<String> get errorStream => _errorController.stream;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;
  bool get isRecording => _isRecording;
  String? get conversationId => _conversationId;
  
  /// Request microphone permission explicitly
  Future<bool> requestMicrophonePermission() async {
    try {
      var permission = await Permission.microphone.status;

      if (permission.isDenied) {
        permission = await Permission.microphone.request();
      }

      return permission == PermissionStatus.granted;
    } catch (e) {
      debugPrint('❌ Error requesting microphone permission: $e');
      return false;
    }
  }

  /// Initialize the service
  Future<void> initialize() async {
    try {
      _statusController.add(ConversationStatus.connecting);

      // Environment variables should already be loaded in main.dart
      // Check if required environment variables are available
      final agentId = dotenv.env['ELEVENLABS_AGENT_ID'];
      final apiKey = dotenv.env['ELEVENLABS_API_KEY'];

      if (agentId == null || agentId.isEmpty) {
        throw Exception('ELEVENLABS_AGENT_ID not found in environment variables');
      }

      if (apiKey == null || apiKey.isEmpty || apiKey == 'your_api_key_here') {
        throw Exception('Please set your ElevenLabs API key in the .env file');
      }

      // Check and request microphone permission with better iOS handling
      var micPermission = await Permission.microphone.status;
      debugPrint('🎤 Initial microphone permission status: $micPermission');

      // Handle all non-granted states
      if (micPermission != PermissionStatus.granted) {
        if (micPermission == PermissionStatus.permanentlyDenied || micPermission == PermissionStatus.restricted) {
          debugPrint('⚠️ Microphone permission is permanently denied or restricted');
          throw Exception('Microphone permission denied. Please enable in Settings > Privacy & Security > Microphone');
        }

        // Try to request permission
        debugPrint('📱 Requesting microphone permission...');
        micPermission = await Permission.microphone.request();
        debugPrint('🎤 Permission request result: $micPermission');
      }

      // Final check after request - use explicit comparison
      if (micPermission != PermissionStatus.granted) {
        String errorMessage = 'Microphone permission is required for voice chat';

        if (micPermission == PermissionStatus.permanentlyDenied) {
          errorMessage = 'Microphone permission denied. Please enable in Settings > Privacy & Security > Microphone';
        } else if (micPermission == PermissionStatus.denied) {
          errorMessage = 'Microphone permission was denied. Please try again and allow access when prompted';
        } else if (micPermission == PermissionStatus.restricted) {
          errorMessage = 'Microphone access is restricted on this device';
        }

        throw Exception(errorMessage);
      }

      _isInitialized = true;
      _statusController.add(ConversationStatus.disconnected);

      debugPrint('🎤 ElevenLabs Conversational AI service initialized with microphone access');
    } catch (e) {
      _statusController.add(ConversationStatus.error);
      _errorController.add('Failed to initialize: $e');
      debugPrint('❌ Failed to initialize ElevenLabs service: $e');
      rethrow;
    }
  }
  
  /// Start a conversation session
  Future<void> startConversation() async {
    if (!_isInitialized) {
      throw Exception('Service not initialized');
    }
    
    if (_isConnected) {
      debugPrint('⚠️ Conversation already active');
      return;
    }
    
    try {
      _statusController.add(ConversationStatus.connecting);
      
      // Get agent ID from environment
      final agentId = dotenv.env['ELEVENLABS_AGENT_ID'];
      if (agentId == null || agentId.isEmpty) {
        throw Exception('Agent ID not configured');
      }
      
      // Create WebSocket connection with authentication
      final apiKey = dotenv.env['ELEVENLABS_API_KEY'];
      final uri = Uri(
        scheme: ApiConstants.webSocketScheme,
        host: ApiConstants.elevenLabsBaseUrl,
        path: ApiConstants.conversationWebSocketPath,
        queryParameters: {
          'agent_id': agentId,
          'xi-api-key': apiKey!,
        },
      );

      debugPrint('🔗 Connecting to: $uri');

      _webSocketChannel = WebSocketChannel.connect(uri);
      
      // Listen to WebSocket messages
      _webSocketChannel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );
      
      // Send conversation initiation message
      final initiationMessage = ConversationInitiationMessage(
        agentId: agentId,
        conversationConfigOverride: {
          'agent': {
            'prompt': {
              'prompt': 'You are Nathan, an enthusiastic AI fitness coach. Keep responses concise and motivational.',
            },
            'first_message': 'Hey there! I\'m Nathan, your AI fitness coach! 💪 Ready to crush your workout today?',
            'language': 'en',
          },
        },
        customLlmExtraBody: {
          'temperature': 0.7,
          'max_tokens': 150,
        },
        dynamicVariables: {
          'coach_name': 'Nathan',
          'app_name': 'OpenFit',
        },
      );
      
      _sendMessage(initiationMessage);
      
      // Set initial mode
      _modeController.add(ConversationMode.idle);
      
    } catch (e) {
      _statusController.add(ConversationStatus.error);
      _errorController.add('Failed to start conversation: $e');
      debugPrint('❌ Failed to start conversation: $e');
      rethrow;
    }
  }
  
  /// End the conversation session
  Future<void> endConversation() async {
    try {
      if (_isRecording) {
        await stopRecording();
      }
      
      _webSocketChannel?.sink.close();
      _webSocketChannel = null;
      
      _isConnected = false;
      _conversationId = null;
      
      _statusController.add(ConversationStatus.disconnected);
      _modeController.add(ConversationMode.idle);
      
      debugPrint('🔚 Conversation ended');
    } catch (e) {
      debugPrint('❌ Error ending conversation: $e');
    }
  }
  
  /// Start recording audio
  Future<void> startRecording() async {
    if (!_isConnected || _isRecording) return;
    
    try {
      // Get temporary directory for recording
      final tempDir = await getTemporaryDirectory();
      _currentRecordingPath = '${tempDir.path}/voice_recording_${DateTime.now().millisecondsSinceEpoch}.wav';
      
      // Start recording with ElevenLabs recommended settings
      await _audioRecorder.start(
        RecordConfig(
          encoder: AudioEncoder.wav,
          sampleRate: ApiConstants.sampleRate,
          numChannels: ApiConstants.channels,
          bitRate: ApiConstants.bitRate,
        ),
        path: _currentRecordingPath!,
      );
      
      _isRecording = true;
      _recordingStatusController.add(true);
      _modeController.add(ConversationMode.listening);
      
      debugPrint('🎤 Started recording');
    } catch (e) {
      _errorController.add('Failed to start recording: $e');
      debugPrint('❌ Failed to start recording: $e');
      rethrow;
    }
  }
  
  /// Stop recording and send audio
  Future<void> stopRecording() async {
    if (!_isRecording) return;
    
    try {
      final recordingPath = await _audioRecorder.stop();
      _isRecording = false;
      _recordingStatusController.add(false);
      _modeController.add(ConversationMode.processing);
      
      if (recordingPath != null && _isConnected) {
        await _sendAudioChunk(recordingPath);
      }
      
      debugPrint('🎤 Stopped recording');
    } catch (e) {
      _isRecording = false;
      _recordingStatusController.add(false);
      _errorController.add('Failed to stop recording: $e');
      debugPrint('❌ Failed to stop recording: $e');
    }
  }
  
  /// Send audio chunk to ElevenLabs
  Future<void> _sendAudioChunk(String audioPath) async {
    try {
      final audioFile = File(audioPath);
      final audioBytes = await audioFile.readAsBytes();
      final base64Audio = base64Encode(audioBytes);
      
      final audioMessage = UserAudioChunkMessage(userAudioChunk: base64Audio);
      _sendMessage(audioMessage);
      
      // Clean up temporary file
      if (await audioFile.exists()) {
        await audioFile.delete();
      }
      
      debugPrint('📤 Sent audio chunk (${audioBytes.length} bytes)');
    } catch (e) {
      _errorController.add('Failed to send audio: $e');
      debugPrint('❌ Failed to send audio chunk: $e');
    }
  }
  
  /// Send a message through WebSocket
  void _sendMessage(ConversationMessage message) {
    if (_webSocketChannel != null) {
      final jsonMessage = jsonEncode(message.toJson());
      _webSocketChannel!.sink.add(jsonMessage);
      debugPrint('📤 Sent: ${message.type}');
    }
  }
  
  /// Handle incoming WebSocket messages
  void _handleWebSocketMessage(dynamic data) {
    try {
      final jsonData = jsonDecode(data as String) as Map<String, dynamic>;
      final message = ConversationMessage.fromJson(jsonData);
      
      debugPrint('📥 Received: ${message.type}');
      
      switch (message.type) {
        case 'conversation_initiation_metadata':
          _handleInitiationMetadata(message as ConversationInitiationMetadata);
          break;
        case 'user_transcript':
          _handleUserTranscript(message as UserTranscriptMessage);
          break;
        case 'agent_response':
          _handleAgentResponse(message as AgentResponseMessage);
          break;
        case 'audio':
          _handleAudioResponse(message as AudioMessage);
          break;
        case 'ping':
          _handlePing(message as PingMessage);
          break;
        case 'vad_score':
          _handleVadScore(message as VadScoreMessage);
          break;
        default:
          debugPrint('🤷 Unknown message type: ${message.type}');
      }
    } catch (e) {
      debugPrint('❌ Error handling WebSocket message: $e');
    }
  }
  
  /// Handle conversation initiation metadata
  void _handleInitiationMetadata(ConversationInitiationMetadata metadata) {
    _conversationId = metadata.conversationId;
    _isConnected = true;
    _statusController.add(ConversationStatus.connected);
    debugPrint('✅ Conversation started: ${metadata.conversationId}');
  }
  
  /// Handle user transcript
  void _handleUserTranscript(UserTranscriptMessage message) {
    _transcriptionController.add(message.userTranscript);
    debugPrint('👤 User said: ${message.userTranscript}');
  }
  
  /// Handle agent response
  void _handleAgentResponse(AgentResponseMessage message) {
    _responseController.add(message.agentResponse);
    _modeController.add(ConversationMode.speaking);
    debugPrint('🤖 Nathan said: ${message.agentResponse}');
  }
  
  /// Handle audio response
  void _handleAudioResponse(AudioMessage message) async {
    try {
      // Decode base64 audio and play it
      final audioBytes = base64Decode(message.audioBase64);
      await _playAudioBytes(audioBytes);
      debugPrint('🔊 Playing audio response');
    } catch (e) {
      debugPrint('❌ Failed to play audio: $e');
    }
  }
  
  /// Handle ping message
  void _handlePing(PingMessage message) {
    // Respond with pong
    final pongMessage = PongMessage(eventId: message.eventId);
    _sendMessage(pongMessage);
  }
  
  /// Handle VAD score
  void _handleVadScore(VadScoreMessage message) {
    _vadScoreController.add(message.vadScore);
  }
  
  /// Play audio bytes
  Future<void> _playAudioBytes(Uint8List audioBytes) async {
    try {
      // Save audio to temporary file and play
      final tempDir = await getTemporaryDirectory();
      final audioFile = File('${tempDir.path}/response_${DateTime.now().millisecondsSinceEpoch}.wav');
      await audioFile.writeAsBytes(audioBytes);
      
      await _audioPlayer.play(DeviceFileSource(audioFile.path));
      
      // Set mode back to idle after audio finishes
      _audioPlayer.onPlayerComplete.listen((_) {
        _modeController.add(ConversationMode.idle);
      });
      
    } catch (e) {
      debugPrint('❌ Failed to play audio: $e');
      _modeController.add(ConversationMode.idle);
    }
  }
  
  /// Handle WebSocket errors
  void _handleWebSocketError(error) {
    _statusController.add(ConversationStatus.error);
    _errorController.add('Connection error: $error');
    debugPrint('❌ WebSocket error: $error');
  }
  
  /// Handle WebSocket connection closed
  void _handleWebSocketClosed() {
    _isConnected = false;
    _statusController.add(ConversationStatus.disconnected);
    _modeController.add(ConversationMode.idle);
    debugPrint('🔌 WebSocket connection closed');

    // Try to reconnect after a short delay if we were previously connected
    if (_conversationId != null) {
      debugPrint('🔄 Attempting to reconnect...');
      Future.delayed(const Duration(seconds: 2), () {
        if (!_isConnected) {
          startConversation();
        }
      });
    }
  }
  
  /// Send text message directly (for testing)
  Future<void> sendTextMessage(String message) async {
    if (!_isConnected) {
      throw Exception('Not connected to conversation');
    }
    
    // For text messages, we simulate the user transcript
    _transcriptionController.add(message);
    debugPrint('💬 Sent text message: $message');
  }
  
  /// Dispose resources
  Future<void> dispose() async {
    await endConversation();
    await _audioRecorder.dispose();
    await _audioPlayer.dispose();
    
    await _statusController.close();
    await _modeController.close();
    await _transcriptionController.close();
    await _responseController.close();
    await _recordingStatusController.close();
    await _vadScoreController.close();
    await _errorController.close();
    
    _isInitialized = false;
    debugPrint('🧹 ElevenLabs service disposed');
  }
}
