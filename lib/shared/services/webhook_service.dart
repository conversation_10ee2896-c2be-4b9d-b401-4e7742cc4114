import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class WebhookService {
  // Production & test endpoints
  static const _prodUrl = 'https://sciwell.app.n8n.cloud/webhook/632dfbb5-7f3f-456f-bae9-bbcf51e7934b';
  static const _testUrl = 'https://sciwell.app.n8n.cloud/webhook-test/632dfbb5-7f3f-456f-bae9-bbcf51e7934b';

  /// Sends the given [payload] to the Next-Workout webhook.
  ///
  /// If [useTestEndpoint] is true (e.g. running in debug mode), the request
  /// is sent to the test URL, otherwise the production URL is used.
  static Future<void> sendNextWorkoutPayload(
    Map<String, dynamic> payload, {
    bool useTestEndpoint = kDebugMode,
  }) async {
    final url = useTestEndpoint ? _testUrl : _prodUrl;
    final uri = Uri.parse(url);

    final jsonPayload = jsonEncode(payload);
    
    debugPrint('🌐 SENDING WEBHOOK TO N8N:');
    debugPrint('  URL: $url');
    debugPrint('  Endpoint: ${useTestEndpoint ? 'TEST' : 'PRODUCTION'}');
    debugPrint('  Payload size: ${jsonPayload.length} characters');
    debugPrint('  User ID: ${payload['user_id']}');
    debugPrint('  Workout: ${payload['workout_name']}');
    debugPrint('');
    debugPrint('📋 FULL JSON PAYLOAD:');
    debugPrint(jsonPayload);
    debugPrint('');
    
    // Pretty print the JSON for better readability in debug mode
    if (kDebugMode) {
      try {
        const encoder = JsonEncoder.withIndent('  ');
        final prettyJson = encoder.convert(payload);
        debugPrint('📋 PRETTY FORMATTED JSON:');
        debugPrint(prettyJson);
        debugPrint('');
      } catch (e) {
        debugPrint('⚠️ Could not pretty-print JSON: $e');
      }
    }

    try {
      final res = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonPayload,
      );

      debugPrint('🌐 WEBHOOK RESPONSE:');
      debugPrint('  Status Code: ${res.statusCode}');
      debugPrint('  Response Body: ${res.body}');

      if (res.statusCode >= 200 && res.statusCode < 300) {
        debugPrint('✅ Webhook sent successfully to n8n (${res.statusCode})');
      } else {
        debugPrint('❌ Webhook error ${res.statusCode}: ${res.body}');
        throw Exception('Webhook responded with status ${res.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Failed to send webhook to n8n: $e');
      // We don\'t rethrow – webhook failure shouldn\'t crash the app
    }
  }

  /// Test method to send a sample payload to n8n for debugging
  /// This is useful for testing the webhook integration without completing a workout
  static Future<void> sendTestPayload() async {
    final testPayload = {
      'user_id': 'test-user-123',
      'workout_name': 'Test Workout - Logging Check',
      'workout_date': DateTime.now().toUtc().toIso8601String(),
      'planned_workout': {
        'workout_name': 'Test Workout - Logging Check',
        'exercises': [
          {
            'order': 1,
            'exercise': 'Push-ups',
            'planned_sets': 3,
            'planned_reps': 10,
            'planned_weight': 0.0,
            'rest_interval': 60,
          },
          {
            'order': 2,
            'exercise': 'Squats',
            'planned_sets': 3,
            'planned_reps': 15,
            'planned_weight': 0.0,
            'rest_interval': 90,
          },
        ],
      },
      'actual_workout': {
        'workout_name': 'Test Workout - Logging Check',
        'exercises': [
          {
            'exercise': 'Push-ups',
            'actual_sets': [
              {
                'set_order': 1,
                'performed_reps': 10,
                'performed_weight': 0.0,
                'rep_difference': 0,
                'set_feedback_difficulty': 'medium',
              },
              {
                'set_order': 2,
                'performed_reps': 8,
                'performed_weight': 0.0,
                'rep_difference': 2,
                'set_feedback_difficulty': 'hard',
              },
            ],
          },
        ],
      },
      'feedback': 'This is a test workout for JSON logging verification',
      'additional_metrics': {
        'duration': 25,
        'calories_burned': 150,
      },
      'test_mode': true,
      'timestamp': DateTime.now().toIso8601String(),
    };

    debugPrint('🧪 SENDING TEST WEBHOOK PAYLOAD');
    await sendNextWorkoutPayload(testPayload, useTestEndpoint: true);
  }

  /// Enhanced logging method that can be called from anywhere to see payload structure
  static void logPayloadStructure(Map<String, dynamic> payload) {
    debugPrint('');
    debugPrint('🔍 DETAILED PAYLOAD ANALYSIS:');
    debugPrint('=' * 50);
    debugPrint('User ID: ${payload['user_id']}');
    debugPrint('Workout Name: ${payload['workout_name']}');
    debugPrint('Workout Date: ${payload['workout_date']}');
    debugPrint('Feedback: ${payload['feedback']}');
    
    // Analyze planned workout
    final plannedWorkout = payload['planned_workout'] as Map<String, dynamic>?;
    if (plannedWorkout != null) {
      final plannedExercises = plannedWorkout['exercises'] as List?;
      debugPrint('Planned Exercises Count: ${plannedExercises?.length ?? 0}');
      if (plannedExercises != null) {
        for (int i = 0; i < plannedExercises.length; i++) {
          final ex = plannedExercises[i] as Map<String, dynamic>;
          debugPrint('  ${i + 1}. ${ex['exercise']} - ${ex['planned_sets']} sets x ${ex['planned_reps']} reps @ ${ex['planned_weight']}lbs');
        }
      }
    }
    
    // Analyze actual workout
    final actualWorkout = payload['actual_workout'] as Map<String, dynamic>?;
    if (actualWorkout != null) {
      final actualExercises = actualWorkout['exercises'] as List?;
      debugPrint('Actual Exercises Count: ${actualExercises?.length ?? 0}');
      if (actualExercises != null) {
        for (int i = 0; i < actualExercises.length; i++) {
          final ex = actualExercises[i] as Map<String, dynamic>;
          final sets = ex['actual_sets'] as List?;
          debugPrint('  ${i + 1}. ${ex['exercise']} - ${sets?.length ?? 0} sets completed');
          if (sets != null) {
            for (int s = 0; s < sets.length; s++) {
              final set = sets[s] as Map<String, dynamic>;
              debugPrint('     Set ${set['set_order']}: ${set['performed_reps']} reps @ ${set['performed_weight']}lbs (${set['set_feedback_difficulty']})');
            }
          }
        }
      }
    }
    
    // Additional metrics
    final metrics = payload['additional_metrics'] as Map<String, dynamic>?;
    if (metrics != null) {
      debugPrint('Duration: ${metrics['duration']} minutes');
      debugPrint('Calories Burned: ${metrics['calories_burned']}');
    }
    
    debugPrint('=' * 50);
    debugPrint('');
  }
} 