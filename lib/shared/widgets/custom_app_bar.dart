import 'package:flutter/material.dart';
import '../../core/theme/color_palette.dart';
import '../../core/theme/typography.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final bool centerTitle;
  final Widget? leading;

  const CustomAppBar({
    super.key,
    required this.title,
    this.onBackPressed,
    this.actions,
    this.centerTitle = true,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return AppBar(
      backgroundColor: isDark ? AppColorPalette.darkBackground : AppColorPalette.white,
      foregroundColor: isDark ? AppColorPalette.grey100 : AppColorPalette.grey900,
      elevation: 0,
      scrolledUnderElevation: 0,
      centerTitle: centerTitle,
      leading: leading ?? (onBackPressed != null 
        ? IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: isDark ? AppColorPalette.grey100 : AppColorPalette.grey900,
            ),
            onPressed: onBackPressed,
          )
        : null),
      title: Text(
        title,
        style: AppTypography.lightTextTheme.titleLarge?.copyWith(
          color: isDark ? AppColorPalette.grey100 : AppColorPalette.grey900,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
} 