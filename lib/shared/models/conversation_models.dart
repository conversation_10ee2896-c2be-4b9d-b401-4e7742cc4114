import 'dart:convert';

/// Represents the connection status of the conversation
enum ConversationStatus {
  disconnected,
  connecting,
  connected,
  error,
}

/// Represents the current mode of the conversation
enum ConversationMode {
  listening,
  speaking,
  processing,
  idle,
}

/// Base class for all WebSocket messages
abstract class ConversationMessage {
  final String type;
  
  const ConversationMessage({required this.type});
  
  Map<String, dynamic> toJson();
  
  factory ConversationMessage.fromJson(Map<String, dynamic> json) {
    final type = json['type'] as String;
    
    switch (type) {
      case 'conversation_initiation_metadata':
        return ConversationInitiationMetadata.fromJson(json);
      case 'user_transcript':
        return UserTranscriptMessage.fromJson(json);
      case 'agent_response':
        return AgentResponseMessage.fromJson(json);
      case 'audio':
        return AudioMessage.fromJson(json);
      case 'ping':
        return PingMessage.fromJson(json);
      case 'vad_score':
        return VadScoreMessage.fromJson(json);
      case 'client_tool_call':
        return ClientToolCallMessage.from<PERSON>son(json);
      default:
        return UnknownMessage(type: type, data: json);
    }
  }
}

/// Message sent to initiate conversation
class ConversationInitiationMessage extends ConversationMessage {
  final String agentId;
  final Map<String, dynamic>? conversationConfigOverride;
  final Map<String, dynamic>? customLlmExtraBody;
  final Map<String, dynamic>? dynamicVariables;
  
  const ConversationInitiationMessage({
    required this.agentId,
    this.conversationConfigOverride,
    this.customLlmExtraBody,
    this.dynamicVariables,
  }) : super(type: 'conversation_initiation_client_data');
  
  @override
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'type': type,
      'conversation_initiation_client_data': {
        'agent_id': agentId,
      },
    };

    if (conversationConfigOverride != null && conversationConfigOverride!.isNotEmpty) {
      json['conversation_initiation_client_data']['conversation_config_override'] = conversationConfigOverride;
    }
    if (customLlmExtraBody != null && customLlmExtraBody!.isNotEmpty) {
      json['conversation_initiation_client_data']['custom_llm_extra_body'] = customLlmExtraBody;
    }
    if (dynamicVariables != null && dynamicVariables!.isNotEmpty) {
      json['conversation_initiation_client_data']['dynamic_variables'] = dynamicVariables;
    }

    return json;
  }
}

/// Response message with conversation metadata
class ConversationInitiationMetadata extends ConversationMessage {
  final String conversationId;
  final String agentOutputAudioFormat;
  final String userInputAudioFormat;
  
  const ConversationInitiationMetadata({
    required this.conversationId,
    required this.agentOutputAudioFormat,
    required this.userInputAudioFormat,
  }) : super(type: 'conversation_initiation_metadata');
  
  factory ConversationInitiationMetadata.fromJson(Map<String, dynamic> json) {
    final metadata = json['conversation_initiation_metadata_event'] as Map<String, dynamic>;
    return ConversationInitiationMetadata(
      conversationId: metadata['conversation_id'] as String,
      agentOutputAudioFormat: metadata['agent_output_audio_format'] as String,
      userInputAudioFormat: metadata['user_input_audio_format'] as String,
    );
  }
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'conversation_initiation_metadata_event': {
        'conversation_id': conversationId,
        'agent_output_audio_format': agentOutputAudioFormat,
        'user_input_audio_format': userInputAudioFormat,
      },
    };
  }
}

/// User audio chunk message
class UserAudioChunkMessage extends ConversationMessage {
  final String userAudioChunk;
  
  const UserAudioChunkMessage({
    required this.userAudioChunk,
  }) : super(type: 'user_audio_chunk');
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'user_audio_chunk': userAudioChunk,
    };
  }
}

/// User transcript message
class UserTranscriptMessage extends ConversationMessage {
  final String userTranscript;
  
  const UserTranscriptMessage({
    required this.userTranscript,
  }) : super(type: 'user_transcript');
  
  factory UserTranscriptMessage.fromJson(Map<String, dynamic> json) {
    final event = json['user_transcription_event'] as Map<String, dynamic>;
    return UserTranscriptMessage(
      userTranscript: event['user_transcript'] as String,
    );
  }
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'user_transcription_event': {
        'user_transcript': userTranscript,
      },
    };
  }
}

/// Agent response message
class AgentResponseMessage extends ConversationMessage {
  final String agentResponse;
  
  const AgentResponseMessage({
    required this.agentResponse,
  }) : super(type: 'agent_response');
  
  factory AgentResponseMessage.fromJson(Map<String, dynamic> json) {
    final event = json['agent_response_event'] as Map<String, dynamic>;
    return AgentResponseMessage(
      agentResponse: event['agent_response'] as String,
    );
  }
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'agent_response_event': {
        'agent_response': agentResponse,
      },
    };
  }
}

/// Audio response message
class AudioMessage extends ConversationMessage {
  final String audioBase64;
  final int eventId;
  
  const AudioMessage({
    required this.audioBase64,
    required this.eventId,
  }) : super(type: 'audio');
  
  factory AudioMessage.fromJson(Map<String, dynamic> json) {
    final event = json['audio_event'] as Map<String, dynamic>;
    return AudioMessage(
      audioBase64: event['audio_base_64'] as String,
      eventId: event['event_id'] as int,
    );
  }
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'audio_event': {
        'audio_base_64': audioBase64,
        'event_id': eventId,
      },
    };
  }
}

/// Ping message for connection health
class PingMessage extends ConversationMessage {
  final int eventId;
  final int pingMs;
  
  const PingMessage({
    required this.eventId,
    required this.pingMs,
  }) : super(type: 'ping');
  
  factory PingMessage.fromJson(Map<String, dynamic> json) {
    final event = json['ping_event'] as Map<String, dynamic>;
    return PingMessage(
      eventId: event['event_id'] as int,
      pingMs: event['ping_ms'] as int,
    );
  }
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'ping_event': {
        'event_id': eventId,
        'ping_ms': pingMs,
      },
    };
  }
}

/// Pong response message
class PongMessage extends ConversationMessage {
  final int eventId;
  
  const PongMessage({
    required this.eventId,
  }) : super(type: 'pong');
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'event_id': eventId,
    };
  }
}

/// VAD (Voice Activity Detection) score message
class VadScoreMessage extends ConversationMessage {
  final double vadScore;
  
  const VadScoreMessage({
    required this.vadScore,
  }) : super(type: 'vad_score');
  
  factory VadScoreMessage.fromJson(Map<String, dynamic> json) {
    final event = json['vad_score_event'] as Map<String, dynamic>;
    return VadScoreMessage(
      vadScore: (event['vad_score'] as num).toDouble(),
    );
  }
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'vad_score_event': {
        'vad_score': vadScore,
      },
    };
  }
}

/// Client tool call message
class ClientToolCallMessage extends ConversationMessage {
  final String toolName;
  final String toolCallId;
  final Map<String, dynamic> parameters;
  
  const ClientToolCallMessage({
    required this.toolName,
    required this.toolCallId,
    required this.parameters,
  }) : super(type: 'client_tool_call');
  
  factory ClientToolCallMessage.fromJson(Map<String, dynamic> json) {
    final toolCall = json['client_tool_call'] as Map<String, dynamic>;
    return ClientToolCallMessage(
      toolName: toolCall['tool_name'] as String,
      toolCallId: toolCall['tool_call_id'] as String,
      parameters: toolCall['parameters'] as Map<String, dynamic>,
    );
  }
  
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'client_tool_call': {
        'tool_name': toolName,
        'tool_call_id': toolCallId,
        'parameters': parameters,
      },
    };
  }
}

/// Unknown message type
class UnknownMessage extends ConversationMessage {
  final Map<String, dynamic> data;
  
  const UnknownMessage({
    required String type,
    required this.data,
  }) : super(type: type);
  
  @override
  Map<String, dynamic> toJson() => data;
}

/// Chat message for UI display
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? messageId;
  
  const ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.messageId,
  });
  
  ChatMessage copyWith({
    String? text,
    bool? isUser,
    DateTime? timestamp,
    String? messageId,
  }) {
    return ChatMessage(
      text: text ?? this.text,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      messageId: messageId ?? this.messageId,
    );
  }
}
