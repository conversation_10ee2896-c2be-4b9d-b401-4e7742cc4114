import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/services/supabase_service.dart';

/// Custom workout exercise model for creation
class CustomWorkoutExercise {
  final String exerciseId;
  final String name;
  final String description;
  final int sets;
  final List<int> reps;
  final List<double> weights;
  final int restInterval;

  const CustomWorkoutExercise({
    required this.exerciseId,
    required this.name,
    required this.description,
    required this.sets,
    required this.reps,
    required this.weights,
    required this.restInterval,
  });

  CustomWorkoutExercise copyWith({
    String? exerciseId,
    String? name,
    String? description,
    int? sets,
    List<int>? reps,
    List<double>? weights,
    int? restInterval,
  }) {
    return CustomWorkoutExercise(
      exerciseId: exerciseId ?? this.exerciseId,
      name: name ?? this.name,
      description: description ?? this.description,
      sets: sets ?? this.sets,
      reps: reps ?? this.reps,
      weights: weights ?? this.weights,
      restInterval: restInterval ?? this.restInterval,
    );
  }
}

/// Custom workout creation state
class CustomWorkoutState {
  final List<CustomWorkoutExercise> exercises;
  final bool isLoading;
  final String? error;

  const CustomWorkoutState({
    this.exercises = const [],
    this.isLoading = false,
    this.error,
  });

  CustomWorkoutState copyWith({
    List<CustomWorkoutExercise>? exercises,
    bool? isLoading,
    String? error,
  }) {
    return CustomWorkoutState(
      exercises: exercises ?? this.exercises,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Custom workout notifier
class CustomWorkoutNotifier extends StateNotifier<CustomWorkoutState> {
  CustomWorkoutNotifier() : super(const CustomWorkoutState());

  /// Add exercise to workout
  void addExercise({
    required String exerciseId,
    required String name,
    required String description,
    required int sets,
    required List<int> reps,
    required List<double> weights,
    required int restInterval,
  }) {
    final exercise = CustomWorkoutExercise(
      exerciseId: exerciseId,
      name: name,
      description: description,
      sets: sets,
      reps: reps,
      weights: weights,
      restInterval: restInterval,
    );

    state = state.copyWith(
      exercises: [...state.exercises, exercise],
    );
    debugPrint('✅ Exercise added: $name');
  }

  /// Remove exercise from workout
  void removeExercise(int index) {
    if (index >= 0 && index < state.exercises.length) {
      final exerciseName = state.exercises[index].name;
      final exercises = List<CustomWorkoutExercise>.from(state.exercises);
      exercises.removeAt(index);
      
      state = state.copyWith(exercises: exercises);
      debugPrint('✅ Exercise removed: $exerciseName');
    }
  }

  /// Update exercise configuration
  void updateExercise(int index, CustomWorkoutExercise exercise) {
    if (index >= 0 && index < state.exercises.length) {
      final exercises = List<CustomWorkoutExercise>.from(state.exercises);
      exercises[index] = exercise;
      
      state = state.copyWith(exercises: exercises);
      debugPrint('✅ Exercise updated: ${exercise.name}');
    }
  }

  /// Reorder exercises
  void reorderExercises(int oldIndex, int newIndex) {
    final exercises = List<CustomWorkoutExercise>.from(state.exercises);
    
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    
    final exercise = exercises.removeAt(oldIndex);
    exercises.insert(newIndex, exercise);
    
    state = state.copyWith(exercises: exercises);
    debugPrint('✅ Exercises reordered');
  }

  /// Clear all exercises
  void clearExercises() {
    state = state.copyWith(exercises: []);
    debugPrint('✅ All exercises cleared');
  }

  /// Save workout to database
  Future<void> saveWorkout({
    required String name,
    required String description,
  }) async {
    if (state.exercises.isEmpty) {
      throw Exception('Cannot save workout without exercises');
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('Please log in to save workouts');
      }

      if (name.trim().isEmpty) {
        throw Exception('Workout name cannot be empty');
      }

      debugPrint('💾 Saving custom workout...');
      debugPrint('   Name: $name');
      debugPrint('   Description: $description');
      debugPrint('   Exercises: ${state.exercises.length}');
      debugPrint('   User ID: $userId');

      // Step 1: Create workout entry
      final workoutResponse = await SupabaseService.client
          .from('workouts')
          .insert({
            'user_id': userId,
            'name': name,
            'ai_description': description.isNotEmpty ? description : null,
            'is_completed': false,
            'is_active': true,
          })
          .select('id')
          .single();

      final workoutId = workoutResponse['id'] as String;
      debugPrint('✅ Workout created with ID: $workoutId');

      // Step 2: Add exercises to workout
      final workoutExercises = <Map<String, dynamic>>[];
      for (int i = 0; i < state.exercises.length; i++) {
        final exercise = state.exercises[i];
        
        // Convert weights from List<double> to List<int> for database storage
        final weightsAsInt = exercise.weights.map((w) => w.toInt()).toList();
        
        workoutExercises.add({
          'workout_id': workoutId,
          'exercise_id': exercise.exerciseId,
          'name': exercise.name,
          'sets': exercise.sets,
          'reps': exercise.reps,
          'weight': weightsAsInt,
          'rest_interval': exercise.restInterval,
          'order_index': i,
          'completed': false,
        });
      }

      await SupabaseService.client
          .from('workout_exercises')
          .insert(workoutExercises);

      debugPrint('✅ ${workoutExercises.length} exercises added to workout');

      // Clear state after successful save
      state = const CustomWorkoutState();
      
      debugPrint('🎉 Custom workout saved successfully!');

    } catch (e) {
      debugPrint('❌ Failed to save custom workout: $e');
      String errorMessage = e.toString();
      
      // Provide user-friendly error messages
      if (errorMessage.contains('duplicate key')) {
        errorMessage = 'A workout with this name already exists';
      } else if (errorMessage.contains('network')) {
        errorMessage = 'Network connection failed. Please try again.';
      } else if (errorMessage.contains('authentication')) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (errorMessage.contains('Exception:')) {
        errorMessage = errorMessage.replaceFirst('Exception: ', '');
      }
      
      state = state.copyWith(
        isLoading: false,
        error: errorMessage,
      );
      rethrow;
    }
  }

  /// Reset state
  void reset() {
    state = const CustomWorkoutState();
  }
}

/// Custom workout provider
final customWorkoutProvider = StateNotifierProvider<CustomWorkoutNotifier, CustomWorkoutState>((ref) {
  return CustomWorkoutNotifier();
}); 