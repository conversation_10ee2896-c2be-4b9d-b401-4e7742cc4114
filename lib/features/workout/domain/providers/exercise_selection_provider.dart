import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/services/supabase_service.dart';

/// Exercise model for selection
class Exercise {
  final String id;
  final String name;
  final String description;
  final String primaryMuscle;
  final String equipment;
  final String? thumbnailUrl;

  const Exercise({
    required this.id,
    required this.name,
    required this.description,
    required this.primaryMuscle,
    required this.equipment,
    this.thumbnailUrl,
  });

  factory Exercise.fromJson(Map<String, dynamic> json) {
    return Exercise(
      id: json['id'],
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      primaryMuscle: json['primary_muscle'] ?? '',
      equipment: json['equipment'] ?? '',
      thumbnailUrl: json['vertical_video'],
    );
  }
}

/// Exercise selection state
class ExerciseSelectionState {
  final List<Exercise> exercises;
  final List<Exercise> filteredExercises;
  final List<String> categories;
  final bool isLoading;
  final String? error;
  final String searchQuery;
  final String selectedCategory;

  const ExerciseSelectionState({
    this.exercises = const [],
    this.filteredExercises = const [],
    this.categories = const ['All'],
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
    this.selectedCategory = 'All',
  });

  ExerciseSelectionState copyWith({
    List<Exercise>? exercises,
    List<Exercise>? filteredExercises,
    List<String>? categories,
    bool? isLoading,
    String? error,
    String? searchQuery,
    String? selectedCategory,
  }) {
    return ExerciseSelectionState(
      exercises: exercises ?? this.exercises,
      filteredExercises: filteredExercises ?? this.filteredExercises,
      categories: categories ?? this.categories,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategory: selectedCategory ?? this.selectedCategory,
    );
  }
}

/// Exercise selection notifier
class ExerciseSelectionNotifier extends StateNotifier<ExerciseSelectionState> {
  ExerciseSelectionNotifier() : super(const ExerciseSelectionState());

  /// Load exercises from database
  Future<void> loadExercises() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      debugPrint('🔄 Loading exercises from database...');

      final response = await SupabaseService.client
          .from('exercises')
          .select('id, name, description, primary_muscle, equipment, vertical_video')
          .order('name');

      final exercises = (response as List)
          .map((json) => Exercise.fromJson(json))
          .toList();

      // Extract unique categories from primary_muscle
      final categoriesSet = <String>{'All'};
      for (final exercise in exercises) {
        if (exercise.primaryMuscle.isNotEmpty) {
          categoriesSet.add(exercise.primaryMuscle);
        }
      }
      final categories = categoriesSet.toList()..sort();

      debugPrint('✅ Loaded ${exercises.length} exercises');
      debugPrint('📋 Categories: ${categories.join(', ')}');

      state = state.copyWith(
        exercises: exercises,
        filteredExercises: exercises,
        categories: categories,
        isLoading: false,
      );
    } catch (e) {
      debugPrint('❌ Failed to load exercises: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Search exercises
  void searchExercises(String query) {
    state = state.copyWith(searchQuery: query);
    _applyFilters();
  }

  /// Filter by category
  void filterByCategory(String category) {
    state = state.copyWith(selectedCategory: category);
    _applyFilters();
  }

  /// Apply search and category filters
  void _applyFilters() {
    var filtered = state.exercises;

    // Apply category filter
    if (state.selectedCategory != 'All') {
      filtered = filtered.where((exercise) => 
          exercise.primaryMuscle.toLowerCase() == state.selectedCategory.toLowerCase()).toList();
    }

    // Apply search filter
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered = filtered.where((exercise) =>
          exercise.name.toLowerCase().contains(query) ||
          exercise.description.toLowerCase().contains(query) ||
          exercise.primaryMuscle.toLowerCase().contains(query) ||
          exercise.equipment.toLowerCase().contains(query)).toList();
    }

    state = state.copyWith(filteredExercises: filtered);
    debugPrint('🔍 Filtered to ${filtered.length} exercises');
  }

  /// Reset state
  void reset() {
    state = const ExerciseSelectionState();
  }
}

/// Exercise selection provider
final exerciseSelectionProvider = StateNotifierProvider<ExerciseSelectionNotifier, ExerciseSelectionState>((ref) {
  return ExerciseSelectionNotifier();
}); 