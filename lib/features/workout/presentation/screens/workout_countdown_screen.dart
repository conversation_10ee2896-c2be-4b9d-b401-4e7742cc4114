import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../../dashboard/domain/models/today_workout.dart';
import '../../domain/models/workout_session.dart';
import '../../domain/providers/workout_session_provider.dart';

/// 3-2-1 countdown screen before starting workout
class WorkoutCountdownScreen extends ConsumerStatefulWidget {
  final WorkoutSession workoutSession;

  const WorkoutCountdownScreen({
    super.key,
    required this.workoutSession,
  });

  @override
  ConsumerState<WorkoutCountdownScreen> createState() => _WorkoutCountdownScreenState();
}

class _WorkoutCountdownScreenState extends ConsumerState<WorkoutCountdownScreen>
    with TickerProviderStateMixin {
  late AnimationController _countdownController;
  late AnimationController _backgroundController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _breathingAnimation;

  Timer? _countdownTimer;
  int _currentCount = 3;
  bool _showSkipHint = false;
  bool _isSkipped = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startCountdown();
    _showSkipHintAfterDelay();
  }

  void _setupAnimations() {
    _countdownController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _countdownController,
      curve: const Interval(0.0, 0.7, curve: Curves.elasticOut),
    ));

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _countdownController,
      curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
    ));

    _breathingAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeInOut,
    ));

    _backgroundController.repeat(reverse: true);
  }

  void _startCountdown() {
    _countdownController.forward().then((_) {
      if (_currentCount > 1 && !_isSkipped) {
        setState(() {
          _currentCount--;
        });
        _countdownController.reset();
        _startCountdown();
      } else if (!_isSkipped) {
        _navigateToActiveWorkout();
      }
    });

    // Play countdown sound (optional)
    HapticFeedback.mediumImpact();
  }

  void _showSkipHintAfterDelay() {
    Timer(const Duration(milliseconds: 1000), () {
      if (mounted && !_isSkipped) {
        setState(() {
          _showSkipHint = true;
        });
      }
    });
  }

  void _skipCountdown() {
    debugPrint('🎯 _skipCountdown called');
    if (_isSkipped) return;
    
    setState(() {
      _isSkipped = true;
    });
    
    _countdownTimer?.cancel();
    _countdownController.stop();
    HapticFeedback.lightImpact();
    debugPrint('🚀 Calling _navigateToActiveWorkout from _skipCountdown');
    _navigateToActiveWorkout();
    debugPrint('✅ Called _navigateToActiveWorkout from _skipCountdown');
  }

  void _navigateToActiveWorkout() {
    debugPrint('🎯 _navigateToActiveWorkout called');
    // Start the active workout in the session provider
    ref.read(workoutSessionProvider.notifier).startActiveWorkout();
    
    // Navigate to active workout screen
    debugPrint('📝 workoutSession in _navigateToActiveWorkout: ${widget.workoutSession}');
    if (widget.workoutSession != null) {
      context.go('/active-workout', extra: widget.workoutSession);
    } else {
      // This case should ideally not happen if navigation to this screen was correct
      debugPrint('ERROR: workoutSession is null in _navigateToActiveWorkout');
      // Optionally, navigate back or show an error
      if (context.canPop()) context.pop();
    }
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    _countdownController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: GestureDetector(
        onTap: _skipCountdown,
        child: AnimatedBuilder(
          animation: _breathingAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _breathingAnimation.value,
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment.center,
                    radius: 1.0,
                    colors: [
                      AppColorPalette.primaryOrange.withOpacity(0.1),
                      AppColorPalette.darkBackground,
                      AppColorPalette.darkBackground,
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: SafeArea(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Get Ready text
                      Text(
                        'GET READY',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 3.0,
                        ),
                      ),

                      SizedBox(height: screenSize.height * 0.1),

                      // Countdown number
                      AnimatedBuilder(
                        animation: _countdownController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _scaleAnimation.value,
                            child: Opacity(
                              opacity: _opacityAnimation.value,
                              child: Container(
                                width: 200,
                                height: 200,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: AppColorPalette.primaryOrange,
                                    width: 4,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColorPalette.primaryOrange.withOpacity(0.3),
                                      blurRadius: 30,
                                      spreadRadius: 10,
                                    ),
                                  ],
                                ),
                                child: Center(
                                  child: Text(
                                    '$_currentCount',
                                    style: theme.textTheme.displayLarge?.copyWith(
                                      color: AppColorPalette.primaryOrange,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 80,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      SizedBox(height: screenSize.height * 0.1),

                      // First exercise preview
                      if (widget.workoutSession.exercises.isNotEmpty) ...[
                        Text(
                          'First: ${widget.workoutSession.exercises.first.name}',
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppSpacing.sm),
                        Text(
                          '${widget.workoutSession.exercises.first.sets} sets × ${widget.workoutSession.exercises.first.targetReps.isNotEmpty ? widget.workoutSession.exercises.first.targetReps.first : 12} reps',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],

                      const Spacer(),

                      // Skip hint
                      AnimatedOpacity(
                        opacity: _showSkipHint ? 1.0 : 0.0,
                        duration: const Duration(milliseconds: 500),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.lg,
                            vertical: AppSpacing.md,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(30),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.touch_app,
                                color: Colors.white.withOpacity(0.8),
                                size: 20,
                              ),
                              const SizedBox(width: AppSpacing.sm),
                              Text(
                                'Tap anywhere to skip',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.white.withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: AppSpacing.xxl),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
