import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../domain/providers/exercise_selection_provider.dart';

class ExerciseSelectionScreen extends ConsumerStatefulWidget {
  const ExerciseSelectionScreen({super.key});

  @override
  ConsumerState<ExerciseSelectionScreen> createState() => _ExerciseSelectionScreenState();
}

class _ExerciseSelectionScreenState extends ConsumerState<ExerciseSelectionScreen> {
  final _searchController = TextEditingController();
  String _selectedCategory = 'All';
  Exercise? _selectedExercise;
  
  // Exercise configuration
  int _sets = 3;
  List<int> _reps = [10];
  List<double> _weights = [0.0];
  int _restInterval = 60;

  @override
  void initState() {
    super.initState();
    // Load exercises when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(exerciseSelectionProvider.notifier).loadExercises();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final exerciseState = ref.watch(exerciseSelectionProvider);
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: 'Select Exercise',
        onBackPressed: () => Navigator.of(context).pop(),
      ),
      body: Column(
        children: [
          // Search and Filter Section
          _buildSearchAndFilter(exerciseState, theme),
          
          // Exercise List
          Expanded(
            child: _buildExerciseList(exerciseState, theme),
          ),
          
          // Configuration Panel (shown when exercise selected)
          if (_selectedExercise != null)
            _buildConfigurationPanel(theme),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter(ExerciseSelectionState state, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.outline.withOpacity(0.1)),
        ),
      ),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search exercises...',
              prefixIcon: Icon(Icons.search, color: theme.colorScheme.onSurfaceVariant),
              filled: true,
              fillColor: theme.colorScheme.surfaceVariant,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            style: theme.textTheme.bodyLarge,
            onChanged: (value) {
              ref.read(exerciseSelectionProvider.notifier).searchExercises(value);
            },
          ),
          
          const SizedBox(height: 16),
          
          // Category Filter
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: state.categories.length,
              itemBuilder: (context, index) {
                final category = state.categories[index];
                final isSelected = category == _selectedCategory;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                      ref.read(exerciseSelectionProvider.notifier).filterByCategory(category);
                    },
                    backgroundColor: theme.colorScheme.surfaceVariant,
                    selectedColor: AppColorPalette.primaryOrange.withOpacity(0.1),
                    labelStyle: TextStyle(
                      color: isSelected ? AppColorPalette.primaryOrange : theme.colorScheme.onSurfaceVariant,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                    side: BorderSide(
                      color: isSelected ? AppColorPalette.primaryOrange : theme.colorScheme.outline.withOpacity(0.3),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseList(ExerciseSelectionState state, ThemeData theme) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: AppColorPalette.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load exercises',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(exerciseSelectionProvider.notifier).loadExercises();
              },
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.filteredExercises.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No exercises found',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filter',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: state.filteredExercises.length,
      itemBuilder: (context, index) {
        final exercise = state.filteredExercises[index];
        final isSelected = _selectedExercise?.id == exercise.id;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildExerciseCard(exercise, isSelected, theme),
        );
      },
    );
  }

  Widget _buildExerciseCard(Exercise exercise, bool isSelected, ThemeData theme) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedExercise = isSelected ? null : exercise;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? AppColorPalette.primaryOrange.withOpacity(0.05) : theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? AppColorPalette.primaryOrange : theme.colorScheme.outline.withOpacity(0.1),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Exercise Image/Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColorPalette.primaryOrange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: exercise.thumbnailUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        exercise.thumbnailUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.fitness_center,
                            color: AppColorPalette.primaryOrange,
                            size: 28,
                          );
                        },
                      ),
                    )
                  : Icon(
                      Icons.fitness_center,
                      color: AppColorPalette.primaryOrange,
                      size: 28,
                    ),
            ),
            
            const SizedBox(width: 16),
            
            // Exercise Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exercise.name,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (exercise.description.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      exercise.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      if (exercise.primaryMuscle.isNotEmpty)
                        _buildInfoChip(exercise.primaryMuscle, Icons.accessibility_new, theme),
                      const SizedBox(width: 8),
                      if (exercise.equipment.isNotEmpty)
                        _buildInfoChip(exercise.equipment, Icons.fitness_center, theme),
                    ],
                  ),
                ],
              ),
            ),
            
            // Selection Indicator
            if (isSelected)
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: AppColorPalette.primaryOrange,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, IconData icon, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: theme.colorScheme.onSurfaceVariant),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigurationPanel(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border(
          top: BorderSide(color: theme.colorScheme.outline.withOpacity(0.1)),
        ),
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Exercise Info
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedExercise!.name,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Configure this exercise, then add to your workout',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedExercise = null;
                    });
                  },
                  child: Text('Cancel'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Configuration Controls
            Row(
              children: [
                Expanded(
                  child: _buildConfigControl(
                    'Sets',
                    _sets.toString(),
                    () => setState(() => _sets = (_sets - 1).clamp(1, 10)),
                    () => setState(() => _sets = (_sets + 1).clamp(1, 10)),
                    theme,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildConfigControl(
                    'Reps',
                    _reps.first.toString(),
                    () => setState(() => _reps = [(_reps.first - 1).clamp(1, 50)]),
                    () => setState(() => _reps = [(_reps.first + 1).clamp(1, 50)]),
                    theme,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildConfigControl(
                    'Weight (lbs)',
                    _weights.first.toInt().toString(),
                    () => setState(() => _weights = [(_weights.first - 5).clamp(0, 1000)]),
                    () => setState(() => _weights = [(_weights.first + 5).clamp(0, 1000)]),
                    theme,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Add Exercise Button
            SizedBox(
              width: double.infinity,
              child: PrimaryButton(
                text: 'Add to Workout',
                onPressed: _addExerciseToWorkout,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigControl(String label, String value, VoidCallback onDecrease, VoidCallback onIncrease, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildControlButton(Icons.remove, onDecrease, theme),
              Expanded(
                child: Text(
                  value,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              _buildControlButton(Icons.add, onIncrease, theme),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildControlButton(IconData icon, VoidCallback onPressed, ThemeData theme) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: AppColorPalette.primaryOrange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          icon,
          size: 16,
          color: AppColorPalette.primaryOrange,
        ),
      ),
    );
  }

  void _addExerciseToWorkout() {
    if (_selectedExercise == null) return;

    // Return exercise configuration data
    Navigator.of(context).pop({
      'exercise_id': _selectedExercise!.id,
      'name': _selectedExercise!.name,
      'description': _selectedExercise!.description,
      'sets': _sets,
      'reps': _reps,
      'weights': _weights,
      'rest_interval': _restInterval,
    });
  }
}

 