import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../dashboard/domain/providers/dashboard_provider.dart';
import '../../domain/providers/workout_session_provider.dart';

/// Loading screen shown during workout preparation
class WorkoutLoadingScreen extends ConsumerStatefulWidget {
  final String? workoutId;

  const WorkoutLoadingScreen({
    super.key,
    this.workoutId,
  });

  @override
  ConsumerState<WorkoutLoadingScreen> createState() => _WorkoutLoadingScreenState();
}

class _WorkoutLoadingScreenState extends ConsumerState<WorkoutLoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  final List<LoadingStep> _loadingSteps = [
    LoadingStep('Loading exercises', Duration(milliseconds: 400)),
    LoadingStep('Downloading videos', Duration(milliseconds: 1100)),
    LoadingStep('Setting up timer', Duration(milliseconds: 500)),
  ];

  int _currentStepIndex = 0;
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startLoadingProcess();
  }

  void _setupAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  void _startLoadingProcess() async {
    try {
      debugPrint('🔄 Starting workout loading process...');
      
      // Add timeout to prevent hanging
      await Future.any([
        _performLoadingSteps(),
        Future.delayed(const Duration(seconds: 30), () {
          throw TimeoutException('Workout loading timed out after 30 seconds');
        }),
      ]);

    } catch (e, stackTrace) {
      debugPrint('❌ Error in workout loading process: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      _showError(e.toString());
    }
  }

  Future<void> _performLoadingSteps() async {
    // Get the specific workout or today's workout
    debugPrint('📋 Fetching workouts...');
    final allWorkouts = await ref.read(allWorkoutsProvider.future);
    debugPrint('📋 Found ${allWorkouts.length} workouts');

    // Find the specific workout if ID is provided
    final targetWorkout = widget.workoutId != null
        ? allWorkouts.firstWhere(
            (w) => w.id == widget.workoutId,
            orElse: () => allWorkouts.isNotEmpty ? allWorkouts.first : throw Exception('No workout found'),
          )
        : allWorkouts.isNotEmpty
            ? allWorkouts.first
            : throw Exception('No workouts available');

    debugPrint('🎯 Target workout: ${targetWorkout.name}');

    // Start the workout flow
    debugPrint('🚀 Starting workout flow...');
    await ref.read(workoutSessionProvider.notifier).startWorkoutFlow(targetWorkout);
    debugPrint('✅ Workout flow started successfully');

    // Animate through loading steps
    _progressController.forward();

    for (int i = 0; i < _loadingSteps.length; i++) {
      if (mounted) {
        setState(() {
          _currentStepIndex = i;
        });
        debugPrint('📱 Loading step ${i + 1}/${_loadingSteps.length}: ${_loadingSteps[i].title}');
      }

      await Future.delayed(_loadingSteps[i].duration);
    }

    if (mounted) {
      setState(() {
        _isComplete = true;
      });
    }

    // Navigate to countdown screen after a brief delay
    await Future.delayed(const Duration(milliseconds: 500));

    if (mounted) {
      // Wait a bit more and check the provider state multiple times
      debugPrint('🔍 First check of workout session provider...');
      var workoutSessionState = ref.read(workoutSessionProvider);
      var workoutSession = workoutSessionState.currentWorkout;
      
      debugPrint('🎯 Workout session state (1st check): ${workoutSessionState.flowStatus}');
      debugPrint('🎯 Workout session for navigation (1st check): ${workoutSession?.name}');
      debugPrint('🎯 Workout session exercises (1st check): ${workoutSession?.exercises.length ?? 0}');
      
      // If the workout session is null, wait a bit more and try again
      if (workoutSession == null) {
        debugPrint('⏳ Workout session is null, waiting and retrying...');
        await Future.delayed(const Duration(milliseconds: 1000));
        
        if (mounted) {
          debugPrint('🔍 Second check of workout session provider...');
          workoutSessionState = ref.read(workoutSessionProvider);
          workoutSession = workoutSessionState.currentWorkout;
          
          debugPrint('🎯 Workout session state (2nd check): ${workoutSessionState.flowStatus}');
          debugPrint('🎯 Workout session for navigation (2nd check): ${workoutSession?.name}');
          debugPrint('🎯 Workout session exercises (2nd check): ${workoutSession?.exercises.length ?? 0}');
        }
      }
      
      if (workoutSession != null) {
        // Validate that the workout session has exercises
        if (workoutSession.exercises.isEmpty) {
          debugPrint('⚠️ Workout session has no exercises, showing error');
          _showError("This workout has no exercises. Please try selecting a different workout.");
          return;
        }
        
        debugPrint('🚀 Navigating to countdown screen...');
        context.go('/workout-countdown', extra: workoutSession);
      } else {
        // Handle the case where workoutSession is unexpectedly null
        debugPrint('❌ Workout session is still null after retries, showing error');
        debugPrint('❌ Workout session state error: ${workoutSessionState.error}');
        debugPrint('❌ Workout session state flowStatus: ${workoutSessionState.flowStatus}');
        debugPrint('❌ Workout session state isLoading: ${workoutSessionState.isLoading}');
        _showError(workoutSessionState.error ?? "Failed to initialize workout session. Please try again.");
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      setState(() {
        _isComplete = true;
      });
    }

    // Show user-friendly error dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => GlassMorphismCard(
        child: AlertDialog(
          backgroundColor: Colors.transparent,
          contentPadding: EdgeInsets.zero,
          content: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.error_outline,
                  color: AppColorPalette.primaryOrange,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  'Workout Setup Failed',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  _getFriendlyErrorMessage(message),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          context.go('/');
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.white,
                          side: const BorderSide(color: Colors.white),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('Go Back'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          setState(() {
                            _currentStepIndex = 0;
                            _isComplete = false;
                          });
                          _progressController.reset();
                          _startLoadingProcess();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColorPalette.primaryOrange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('Try Again'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getFriendlyErrorMessage(String error) {
    if (error.contains('No workout')) {
      return 'No workouts available. Please check your internet connection or try again later.';
    } else if (error.contains('User not authenticated')) {
      return 'Please sign in to continue with your workout.';
    } else if (error.contains('network') || error.contains('connection')) {
      return 'Network connection issue. Please check your internet and try again.';
    } else if (error.contains('timed out') || error.contains('timeout')) {
      return 'The workout setup is taking longer than expected. Please check your internet connection and try again.';
    } else if (error.contains('video') || error.contains('preload')) {
      return 'Having trouble loading workout videos. Your workout will continue without video previews.';
    } else {
      return 'Something went wrong while preparing your workout. Please try again.';
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo with pulse animation
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: AppColorPalette.primaryGradient,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColorPalette.primaryOrange.withOpacity(0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.fitness_center,
                        color: Colors.white,
                        size: 60,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: AppSpacing.xxl),

              // OpenFit title
              Text(
                'OpenFit',
                style: theme.textTheme.headlineLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.5,
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Progress bar
              GlassMorphismCard(
                padding: const EdgeInsets.all(AppSpacing.md),
                child: Column(
                  children: [
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return LinearProgressIndicator(
                          value: _progressAnimation.value,
                          backgroundColor: Colors.white.withOpacity(0.1),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColorPalette.primaryOrange,
                          ),
                          minHeight: 8,
                        );
                      },
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      'Preparing your workout',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Loading steps
              Column(
                children: _loadingSteps.asMap().entries.map((entry) {
                  final index = entry.key;
                  final step = entry.value;
                  final isActive = index == _currentStepIndex && !_isComplete;
                  final isCompleted = index < _currentStepIndex || _isComplete;

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                    child: Row(
                      children: [
                        // Status icon
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: isCompleted 
                                ? AppColorPalette.successGreen
                                : isActive 
                                    ? AppColorPalette.primaryOrange
                                    : Colors.white.withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                          child: isCompleted
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : isActive
                                  ? SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    )
                                  : null,
                        ),

                        const SizedBox(width: AppSpacing.md),

                        // Step text
                        Text(
                          step.title,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: isCompleted || isActive
                                ? Colors.white
                                : Colors.white.withOpacity(0.6),
                            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: AppSpacing.xxl),

              // Cancel button
              TextButton(
                onPressed: () {
                  context.go('/');
                },
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Loading step model
class LoadingStep {
  final String title;
  final Duration duration;

  const LoadingStep(this.title, this.duration);
}
