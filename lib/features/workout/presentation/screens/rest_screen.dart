import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/workout_session.dart';
import '../widgets/workout_timer.dart';
import '../widgets/ai_coach_avatar.dart';

class RestScreen extends ConsumerStatefulWidget {
  final Duration restDuration;
  final bool isLastSet;
  final WorkoutExercise? nextExercise;
  final VoidCallback? onRestComplete;
  final VoidCallback? onSkip;

  const RestScreen({
    super.key,
    required this.restDuration,
    this.isLastSet = false,
    this.nextExercise,
    this.onRestComplete,
    this.onSkip,
  });

  @override
  ConsumerState<RestScreen> createState() => _RestScreenState();
}

class _RestScreenState extends ConsumerState<RestScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  Timer? _restTimer;
  Duration _remainingTime = Duration.zero;
  bool _isPaused = false;
  int _difficultyRating = 3; // 1-5 scale

  @override
  void initState() {
    super.initState();
    _remainingTime = widget.restDuration;
    _setupAnimations();
    _startAnimations();
    _startRestTimer();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Curves.easeOut,
      ),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: FitnessAnimationCurves.springCurve,
      ),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _scaleController.forward();
    });
  }

  void _startRestTimer() {
    _restTimer?.cancel();
    _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isPaused && mounted) {
        setState(() {
          if (_remainingTime.inSeconds > 0) {
            _remainingTime = Duration(seconds: _remainingTime.inSeconds - 1);
          } else {
            _completeRest();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _restTimer?.cancel();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _fadeController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildHeader(context, theme),
                      Expanded(
                        child: _buildMainContent(context, theme),
                      ),
                      _buildBottomActions(context, theme),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.isLastSet ? 'Exercise Complete!' : 'Rest Time',
          style: theme.textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w700,
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.close,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildMainContent(BuildContext context, ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Countdown timer
        Container(
          width: 200,
          height: 200,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Circular progress indicator
              SizedBox(
                width: 200,
                height: 200,
                child: CircularProgressIndicator(
                  value: 1.0 - (_remainingTime.inSeconds / widget.restDuration.inSeconds),
                  strokeWidth: 8,
                  backgroundColor: Colors.white.withOpacity(0.1),
                  valueColor: AlwaysStoppedAnimation<Color>(AppColorPalette.primaryOrange),
                ),
              ),
              // Timer text
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${_remainingTime.inMinutes}:${(_remainingTime.inSeconds % 60).toString().padLeft(2, '0')}',
                    style: AppTypography.displayNumbers(
                      fontSize: 48,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    _isPaused ? 'PAUSED' : 'REST TIME',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 40),
        
        // Time adjustment buttons
        _buildTimeAdjustmentButtons(theme),
        
        const SizedBox(height: 40),
        
        // Difficulty rating (between exercises)
        if (widget.isLastSet && widget.nextExercise != null)
          _buildDifficultyRating(theme),
        
        // Next exercise preview
        if (widget.nextExercise != null)
          _buildNextExercisePreview(theme),
        
        const SizedBox(height: 40),
        
        // AI Coach
        AICoachAvatar(
          message: _getCoachMessage(),
          size: 80,
        ),
      ],
    );
  }

  Widget _buildTimeAdjustmentButtons(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildTimeButton(
          label: '-15s',
          onPressed: () => _adjustTime(-15),
          theme: theme,
        ),
        const SizedBox(width: 20),
        _buildTimeButton(
          label: '+15s',
          onPressed: () => _adjustTime(15),
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildTimeButton({
    required String label,
    required VoidCallback onPressed,
    required ThemeData theme,
  }) {
    return GlassMorphismCard(
      onTap: onPressed,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Text(
        label,
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildDifficultyRating(ThemeData theme) {
    return GlassMorphismCard(
      child: Column(
        children: [
          Text(
            'How was that set?',
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(5, (index) {
              final rating = index + 1;
              final isSelected = rating == _difficultyRating;
              
              return GestureDetector(
                onTap: () => _updateDifficultyRating(rating),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColorPalette.primaryOrange
                        : Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected 
                          ? AppColorPalette.primaryOrange
                          : Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      rating.toString(),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Too Easy',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
              Text(
                'Perfect',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
              Text(
                'Too Hard',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNextExercisePreview(ThemeData theme) {
    return GlassMorphismCard(
      child: Row(
        children: [
          // Exercise thumbnail
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColorPalette.primaryOrange.withOpacity(0.3),
                  AppColorPalette.primaryOrangeLight.withOpacity(0.3),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.fitness_center,
              color: AppColorPalette.primaryOrange,
              size: 24,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Exercise info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Next Exercise',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.nextExercise!.name,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.nextExercise!.sets} sets • ${widget.nextExercise!.currentTargetReps} reps',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context, ThemeData theme) {
    return Row(
      children: [
        // Skip rest button
        Expanded(
          child: OutlinedButton(
            onPressed: _skipRest,
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white, width: 1),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('Skip Rest'),
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Pause/Resume button
        Expanded(
          child: ElevatedButton(
            onPressed: _togglePause,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColorPalette.primaryOrange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(_isPaused ? 'Resume' : 'Pause'),
          ),
        ),
      ],
    );
  }

  String _getCoachMessage() {
    if (widget.isLastSet) {
      return "Great set! How did it feel?";
    } else if (_remainingTime.inSeconds > 30) {
      return "Take your time to recover 😌";
    } else {
      return "Almost ready for the next set! 💪";
    }
  }

  void _adjustTime(int seconds) {
    HapticFeedback.lightImpact();
    if (mounted) {
      setState(() {
        _remainingTime = Duration(
          seconds: (_remainingTime.inSeconds + seconds).clamp(0, 300),
        );
      });
    }
  }

  void _updateDifficultyRating(int rating) {
    HapticFeedback.lightImpact();
    if (mounted) {
      setState(() {
        _difficultyRating = rating;
      });
    }

    // Show weight adjustment suggestions based on difficulty
    _showWeightSuggestions(rating);
  }

  void _showWeightSuggestions(int difficulty) {
    String suggestion = '';
    String emoji = '';
    double adjustmentPercentage = 0;

    switch (difficulty) {
      case 1: // Too easy
        suggestion = 'Try increasing weight by 5-10%';
        emoji = '📈';
        adjustmentPercentage = 0.075; // 7.5% increase
        break;
      case 2: // Easy
        suggestion = 'Consider increasing weight by 2.5-5%';
        emoji = '⬆️';
        adjustmentPercentage = 0.0375; // 3.75% increase
        break;
      case 3: // Perfect
        suggestion = 'Perfect weight! Keep it up!';
        emoji = '🎯';
        adjustmentPercentage = 0;
        break;
      case 4: // Hard
        suggestion = 'Consider decreasing weight by 2.5-5%';
        emoji = '⬇️';
        adjustmentPercentage = -0.0375; // 3.75% decrease
        break;
      case 5: // Too hard
        suggestion = 'Try decreasing weight by 5-10%';
        emoji = '📉';
        adjustmentPercentage = -0.075; // 7.5% decrease
        break;
    }

    if (suggestion.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Text(
                emoji,
                style: const TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'AI Coach Suggestion',
                      style: TextStyle(
                        color: AppColorPalette.primaryOrange,
                        fontWeight: FontWeight.w700,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      suggestion,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: AppColorPalette.darkCard.withOpacity(0.9),
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  void _togglePause() {
    HapticFeedback.lightImpact();
    if (mounted) {
      setState(() {
        _isPaused = !_isPaused;
      });
    }
  }

  void _skipRest() {
    HapticFeedback.mediumImpact();
    _completeRest();
  }

  void _completeRest() {
    _restTimer?.cancel();
    widget.onRestComplete?.call();
    if (mounted && Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }
}
