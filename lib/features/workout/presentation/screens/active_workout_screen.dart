import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/workout_session.dart';
import '../../domain/providers/workout_session_provider.dart';
import '../widgets/workout_timer.dart';
import '../widgets/exercise_video_player.dart';
import '../widgets/set_counter_widget.dart';
import '../widgets/ai_coach_avatar.dart';
import '../widgets/exercise_progress_bar.dart';
import 'workout_completion_screen.dart';
import 'rest_screen.dart';

class ActiveWorkoutScreen extends ConsumerStatefulWidget {
  final WorkoutSession workout;

  const ActiveWorkoutScreen({
    super.key,
    required this.workout,
  });

  @override
  ConsumerState<ActiveWorkoutScreen> createState() => _ActiveWorkoutScreenState();
}

class _ActiveWorkoutScreenState extends ConsumerState<ActiveWorkoutScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  Timer? _workoutTimer;
  Duration _elapsedTime = Duration.zero;
  bool _isResting = false;
  bool _isNavigating = false;
  bool _isProcessingSet = false;
  bool _isMovingToNextSet = false;
  
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
    _startWorkoutTimer();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasInitialized) {
      _initializeCurrentSet();
      _hasInitialized = true;
    }
  }

  void _initializeCurrentSet() {
    setState(() {
      _currentReps = _currentExercise.currentTargetReps;
      _currentWeight = _currentExercise.currentTargetWeight;
    });
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _fadeController,
        curve: Curves.easeOut,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: FitnessAnimationCurves.springCurve,
      ),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  void _startWorkoutTimer() {
    _workoutTimer?.cancel();
    _workoutTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && !_isResting) {
        setState(() {
          _elapsedTime = Duration(seconds: _elapsedTime.inSeconds + 1);
        });
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _workoutTimer?.cancel();
    super.dispose();
  }

  WorkoutExercise get _currentExercise {
    final sessionState = ref.watch(workoutSessionProvider);
    if (sessionState.currentWorkout != null) {
      return sessionState.currentWorkout!.exercises[sessionState.currentExerciseIndex];
    }
    return widget.workout.exercises[0]; // Fallback
  }

  int get _currentExerciseIndex {
    final sessionState = ref.watch(workoutSessionProvider);
    return sessionState.currentExerciseIndex;
  }

  int get _currentSetIndex {
    final sessionState = ref.watch(workoutSessionProvider);
    return sessionState.currentSetIndex;
  }

  double get _overallProgress => (_currentExerciseIndex + 1) / widget.workout.exercises.length;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _fadeController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  children: [
                    _buildTopSection(context, theme),
                    Expanded(
                      child: _buildMainContent(context, theme),
                    ),
                    _buildBottomNavigation(context, theme),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTopSection(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Header with timer and progress
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Back button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () => _showPauseDialog(context),
                  icon: const Icon(Icons.pause, color: Colors.white),
                ),
              ),
              
              // Timer and progress
              Column(
                children: [
                  WorkoutTimer(
                    elapsedTime: _elapsedTime,
                    textColor: Colors.white,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${(_overallProgress * 100).toInt()}% Complete',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              
              // Menu button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () => _showWorkoutMenu(context),
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Exercise progress bar
          ExerciseProgressBar(
            exercises: widget.workout.exercises,
            currentIndex: _currentExerciseIndex,
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, ThemeData theme) {
    return Column(
      children: [
        // Video player section
        Expanded(
          flex: 3,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            child: ExerciseVideoPlayer(
              exercise: _currentExercise,
              onTap: _toggleVideoPlayback,
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Exercise info and controls
        Expanded(
          flex: 2,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                // Exercise name and set info
                Text(
                  _currentExercise.name,
                  style: AppTypography.displayNumbers(
                    fontSize: 24,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 6),
                
                Text(
                  _currentExercise.progressText,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Set counter
                Flexible(
                  child: SetCounterWidget(
                    currentReps: _currentReps > 0 ? _currentReps : _currentExercise.currentTargetReps,
                    currentWeight: _currentWeight > 0 ? _currentWeight : _currentExercise.currentTargetWeight,
                    onRepsChanged: _updateReps,
                    onWeightChanged: _updateWeight,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavigation(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // AI Coach Avatar
          const AICoachAvatar(),
          
          const SizedBox(height: 8),
          
          // Next exercise preview
          if (_currentExerciseIndex < widget.workout.exercises.length - 1)
            _buildNextExercisePreview(theme),
          
          if (_currentExerciseIndex < widget.workout.exercises.length - 1)
            const SizedBox(height: 8),
          
          // Action buttons
          Row(
            children: [
              // Skip set button
              Expanded(
                child: OutlinedButton(
                  onPressed: (_isProcessingSet || _isNavigating || _currentExercise.completedSets >= _currentExercise.sets) ? null : _skipSet,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: (_isProcessingSet || _isNavigating || _currentExercise.completedSets >= _currentExercise.sets) 
                        ? Colors.white.withOpacity(0.5) 
                        : Colors.white,
                    side: BorderSide(
                      color: (_isProcessingSet || _isNavigating || _currentExercise.completedSets >= _currentExercise.sets) 
                          ? Colors.white.withOpacity(0.3) 
                          : Colors.white, 
                      width: 1
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    _isProcessingSet ? 'Processing...' : 'Skip Set',
                    style: TextStyle(
                      color: (_isProcessingSet || _isNavigating || _currentExercise.completedSets >= _currentExercise.sets) 
                          ? Colors.white.withOpacity(0.5) 
                          : Colors.white,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Complete set button
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: (_isProcessingSet || _isNavigating || _currentExercise.completedSets >= _currentExercise.sets) ? null : _completeSet,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColorPalette.primaryOrange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    _isProcessingSet ? 'Processing...' : 
                    (_currentExercise.completedSets >= _currentExercise.sets ? 'Exercise Complete' : 
                    (_currentExercise.isLastSet ? 'Complete Exercise' : 'Complete Set')),
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNextExercisePreview(ThemeData theme) {
    final nextExercise = widget.workout.exercises[_currentExerciseIndex + 1];
    
    return GlassMorphismCard(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          Icon(
            Icons.skip_next,
            color: AppColorPalette.primaryOrange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Next: ${nextExercise.name}',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _toggleVideoPlayback() {
    HapticFeedback.lightImpact();
    // TODO: Implement video playback toggle
  }

  // Track current set values
  int _currentReps = 0;
  double _currentWeight = 0.0;

  void _updateReps(int reps) {
    setState(() {
      _currentReps = reps;
    });
  }

  void _updateWeight(double weight) {
    setState(() {
      _currentWeight = weight;
    });
  }

  void _skipSet() async {
    // Prevent multiple simultaneous operations
    if (_isProcessingSet || _isNavigating) {
      debugPrint('⚠️ Operation already in progress, ignoring skip set');
      return;
    }

    // Check if exercise is already completed
    if (_currentExercise.completedSets >= _currentExercise.sets) {
      debugPrint('⚠️ Exercise already completed, moving to next exercise');
      _completeExercise();
      return;
    }

    setState(() {
      _isProcessingSet = true;
    });

    HapticFeedback.lightImpact();

    debugPrint('⏭️ ACTIVE WORKOUT: Skip Set Button Pressed');
    debugPrint('  Current Exercise: ${_currentExercise.name}');
    debugPrint('  Current Set Index: $_currentSetIndex');
    debugPrint('  Is Last Set: ${_currentExercise.isLastSet}');

    try {
      // Skip the set using the workout session provider
      await ref.read(workoutSessionProvider.notifier).skipSet();

      // Show skip feedback
      _showSkipSetFeedback();

      // Check if this was the last set AFTER skipping it
      final updatedExercise = _currentExercise;
      debugPrint('🔍 AFTER SET SKIP:');
      debugPrint('  Updated Exercise Completed Sets: ${updatedExercise.completedSets}');
      debugPrint('  Updated Is Last Set: ${updatedExercise.isLastSet}');

      // Use completed sets count to determine completion (same logic as complete set)
      if (updatedExercise.completedSets >= updatedExercise.sets) {
        debugPrint('🏁 EXERCISE COMPLETED - Moving to next exercise');
        _completeExercise();
      } else {
        debugPrint('➡️ MORE SETS REMAINING - Moving to next set');
        _moveToNextSet();
      }
    } catch (e) {
      debugPrint('❌ ERROR skipping set: $e');
      // Show user-friendly error message
      _showErrorMessage('Unable to skip set properly. Continuing with workout.');

      // Continue with workout flow even if skip failed
      if (_currentExercise.completedSets >= _currentExercise.sets) {
        _completeExercise();
      } else {
        _moveToNextSet();
      }
    } finally {
      // Always reset the processing flag
      if (mounted) {
        setState(() {
          _isProcessingSet = false;
        });
      }
    }
  }

  void _completeSet() async {
    // Prevent multiple simultaneous set completions
    if (_isProcessingSet || _isNavigating) {
      debugPrint('⚠️ Set completion already in progress, ignoring');
      return;
    }

    // Check if exercise is already completed
    if (_currentExercise.completedSets >= _currentExercise.sets) {
      debugPrint('⚠️ Exercise already completed, moving to next exercise');
      _completeExercise();
      return;
    }

    setState(() {
      _isProcessingSet = true;
    });

    HapticFeedback.heavyImpact();

    // Show immediate visual feedback
    _showSetCompletionFeedback();

    debugPrint('🎯 ACTIVE WORKOUT: Complete Set Button Pressed');
    debugPrint('  Current Exercise: ${_currentExercise.name}');
    debugPrint('  Current Set Index: $_currentSetIndex');
    debugPrint('  Is Last Set: ${_currentExercise.isLastSet}');

    try {
      // Save completed set to database
      final prInfo = await ref.read(workoutSessionProvider.notifier).completeSet(
        performedReps: _currentReps > 0 ? _currentReps : _currentExercise.currentTargetReps,
        performedWeight: _currentWeight > 0 ? _currentWeight : _currentExercise.currentTargetWeight,
        difficulty: null, // We'll add difficulty rating in rest screen
      );

      // Check for personal records and celebrate
      if (prInfo['is_pr'] == true) {
        _celebratePersonalRecord(prInfo);
      }

      // Additional success feedback
      HapticFeedback.lightImpact();

      // Check if this was the last set AFTER completing it
      final updatedExercise = _currentExercise;
      debugPrint('🔍 AFTER SET COMPLETION:');
      debugPrint('  Updated Exercise Completed Sets: ${updatedExercise.completedSets}');
      debugPrint('  Updated Is Last Set: ${updatedExercise.isLastSet}');

      // Use completed sets count to determine completion
      if (updatedExercise.completedSets >= updatedExercise.sets) {
        debugPrint('🏁 EXERCISE COMPLETED - Moving to next exercise');
        _completeExercise();
      } else {
        debugPrint('➡️ MORE SETS REMAINING - Moving to next set');
        _moveToNextSet();
      }
    } catch (e) {
      debugPrint('❌ ERROR completing set: $e');
      // Show user-friendly error message
      _showErrorMessage('Unable to save your progress. Your workout will continue, but this set may not be recorded.');

      // Continue with workout flow even if save failed
      if (_currentExercise.completedSets >= _currentExercise.sets) {
        _completeExercise();
      } else {
        _moveToNextSet();
      }
    } finally {
      // Always reset the processing flag
      if (mounted) {
        setState(() {
          _isProcessingSet = false;
        });
      }
    }
  }

  void _moveToNextSet() {
    if (_isMovingToNextSet || _isNavigating || _isResting) {
      debugPrint('⚠️ _moveToNextSet blocked: isMovingToNextSet=$_isMovingToNextSet, isNavigating=$_isNavigating, isResting=$_isResting');
      return;
    }

    setState(() {
      _isMovingToNextSet = true;
    });

    debugPrint('🔄 MOVING TO NEXT SET');
    debugPrint('🔍 Call stack check: _moveToNextSet called');

    try {
      // Use workout session provider to move to next set
      ref.read(workoutSessionProvider.notifier).nextSet();

      // Start rest timer
      _startRestPeriod();
    } catch (e) {
      debugPrint('❌ Error in _moveToNextSet: $e');
      if (mounted) {
        setState(() {
          _isMovingToNextSet = false;
        });
      }
    }
  }

  void _completeExercise() {
    if (_isNavigating || _isMovingToNextSet) {
      debugPrint('⚠️ _completeExercise blocked: isNavigating=$_isNavigating, isMovingToNextSet=$_isMovingToNextSet');
      return; // Prevent multiple calls
    }
    
    setState(() {
      _isMovingToNextSet = true;
    });

    debugPrint('🏁 COMPLETING EXERCISE');
    
    if (_currentExerciseIndex < widget.workout.exercises.length - 1) {
      // Use the provider's method to advance to next exercise
      ref.read(workoutSessionProvider.notifier).nextExercise();
      _initializeCurrentSet(); // Reset values for new exercise
      _startRestPeriod();
    } else {
      _completeWorkout();
    }
  }

  void _startRestPeriod() {
    debugPrint('🛌 STARTING REST PERIOD');
    debugPrint('🔍 Rest period state: isResting=$_isResting, isMovingToNextSet=$_isMovingToNextSet');
    
    setState(() {
      _isResting = true;
    });
    
    // Navigate to rest screen
    context.push('/rest-screen', extra: {
      'duration': Duration(seconds: _currentExercise.restInterval),
      'isLastSet': _currentExercise.isLastSet,
      'nextExercise': _currentExerciseIndex < widget.workout.exercises.length - 1
          ? widget.workout.exercises[_currentExerciseIndex + 1]
          : null,
      'onRestComplete': () {
        debugPrint('✅ Rest completed - returning to active workout');
        if (mounted && context.canPop()) {
          context.pop();
        }
      },
      'onSkip': () {
        debugPrint('⏭️ Rest skipped - returning to active workout');
        if (mounted && context.canPop()) {
          context.pop();
        }
      },
    }).then((_) {
      debugPrint('🔙 Returned from rest screen');
      if (mounted) {
        setState(() {
          _isResting = false;
          _isMovingToNextSet = false; // Reset the flag when returning from rest
        });
        debugPrint('🔄 State reset: isResting=false, isMovingToNextSet=false');
      }
    });
  }

  void _completeWorkout() {
    if (_isNavigating) return; // Prevent multiple calls
    
    setState(() {
      _isNavigating = true;
      _isMovingToNextSet = false; // Reset the flag for workout completion
    });
    
    debugPrint('🎉 COMPLETING WORKOUT');
    
    // Use WidgetsBinding to ensure UI updates complete first
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || !_isNavigating) return; // Double check
      
      // Use GoRouter to navigate to workout completion then back to home
      context.push('/workout-completion', extra: widget.workout).then((_) {
        // After completion screen, go back to dashboard
        if (mounted) {
          context.go('/');
        }
      });
    });
  }

  void _showPauseDialog(BuildContext context) {
    HapticFeedback.lightImpact();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => GlassMorphismCard(
        child: AlertDialog(
          backgroundColor: Colors.transparent,
          contentPadding: EdgeInsets.zero,
          content: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.pause_circle_outline,
                  color: AppColorPalette.primaryOrange,
                  size: 48,
                ),
                const SizedBox(height: 16),
                Text(
                  'Workout Paused',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Your progress has been saved. What would you like to do?',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Column(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _resumeWorkout();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColorPalette.primaryOrange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('Resume Workout'),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _endWorkoutEarly();
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.white,
                          side: const BorderSide(color: Colors.white),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('End Workout'),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () {
                          if (Navigator.of(context).canPop()) {
                            Navigator.of(context).pop();
                          }
                          _saveAndExit();
                        },
                        child: Text(
                          'Save & Exit',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _resumeWorkout() {
    ref.read(workoutSessionProvider.notifier).resumeWorkout();
    _startWorkoutTimer();
  }

  void _endWorkoutEarly() async {
    await ref.read(workoutSessionProvider.notifier).completeWorkout(
      userRating: null,
      userFeedback: 'Workout ended early',
    );
    
    context.pushReplacement('/workout-completion', extra: widget.workout);
  }

  void _saveAndExit() {
    ref.read(workoutSessionProvider.notifier).pauseWorkout();
    context.go('/');
  }

  void _showWorkoutMenu(BuildContext context) {
    HapticFeedback.lightImpact();
    // TODO: Show workout menu
  }

  void _showSetCompletionFeedback() {
    // Show a brief success animation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: AppColorPalette.successGreen,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Set completed! 💪',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        backgroundColor: AppColorPalette.darkCard.withOpacity(0.9),
        duration: const Duration(milliseconds: 1500),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.only(
          bottom: 100,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  void _showSkipSetFeedback() {
    // Show a brief skip animation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.skip_next,
              color: AppColorPalette.primaryOrange,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Set skipped! ⏭️',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        backgroundColor: AppColorPalette.darkCard.withOpacity(0.9),
        duration: const Duration(milliseconds: 1500),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.only(
          bottom: 100,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: AppColorPalette.warningYellow,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColorPalette.darkCard.withOpacity(0.9),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.only(
          bottom: 100,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  void _celebratePersonalRecord(Map<String, dynamic> prInfo) {
    // Extra haptic feedback for PR
    HapticFeedback.heavyImpact();
    
    String prMessage = '';
    String prIcon = '🎉';
    
    switch (prInfo['pr_type']) {
      case 'first_time':
        prMessage = 'First time! 🎯';
        prIcon = '🌟';
        break;
      case 'weight':
        prMessage = 'New Weight PR! ${prInfo['improvement']}';
        prIcon = '💪';
        break;
      case 'reps':
        prMessage = 'New Rep PR! ${prInfo['improvement']}';
        prIcon = '🔥';
        break;
      case 'estimated_1rm':
        prMessage = 'New 1RM PR! ${prInfo['improvement']}';
        prIcon = '🏆';
        break;
      default:
        prMessage = 'Personal Record! 🎉';
    }
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: AppColorPalette.primaryGradient,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    prIcon,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'PERSONAL RECORD!',
                      style: TextStyle(
                        color: AppColorPalette.primaryOrange,
                        fontWeight: FontWeight.w800,
                        fontSize: 12,
                        letterSpacing: 1.2,
                      ),
                    ),
                    Text(
                      prMessage,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        backgroundColor: AppColorPalette.darkCard.withOpacity(0.95),
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: AppColorPalette.primaryOrange.withOpacity(0.3),
            width: 1,
          ),
        ),
        margin: const EdgeInsets.only(
          bottom: 100,
          left: 20,
          right: 20,
        ),
      ),
    );
  }
}
