import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../dashboard/domain/models/today_workout.dart';
import '../../../dashboard/data/repositories/dashboard_repository.dart';
import 'workout_hero_details_screen.dart';
import 'custom_workout_creation_screen.dart';

/// Provider for the current active workout (oldest uncompleted workout)
final currentWorkoutProvider = FutureProvider.autoDispose<TodayWorkout?>((ref) async {
  final repository = DashboardRepository();
  return repository.getCurrentWorkout();
});

/// Provider to trigger refresh of current workout
final currentWorkoutRefreshProvider = StateProvider.autoDispose<int>((ref) => 0);

/// Screen that shows the current active workout (one at a time)
class CurrentWorkoutScreen extends ConsumerStatefulWidget {
  const CurrentWorkoutScreen({super.key});

  @override
  ConsumerState<CurrentWorkoutScreen> createState() => _CurrentWorkoutScreenState();
}

class _CurrentWorkoutScreenState extends ConsumerState<CurrentWorkoutScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentWorkoutAsync = ref.watch(currentWorkoutProvider);
    
    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: currentWorkoutAsync.when(
        data: (workout) {
          if (workout == null) {
            return _buildNoWorkoutScreen();
          }
          // Show the workout hero details screen directly
          return WorkoutHeroDetailsScreen(workout: workout);
        },
        loading: () => _buildLoadingScreen(),
        error: (error, stack) => _buildErrorScreen(error.toString()),
      ),
      floatingActionButton: currentWorkoutAsync.maybeWhen(
        data: (workout) => workout != null ? FloatingActionButton.extended(
          onPressed: () async {
            HapticFeedback.mediumImpact();
            final result = await Navigator.of(context).push<bool>(
              MaterialPageRoute(
                builder: (context) => const CustomWorkoutCreationScreen(),
              ),
            );
            
            // Refresh workouts if a new one was created
            if (result == true) {
              ref.invalidate(currentWorkoutProvider);
            }
          },
          icon: const Icon(Icons.add),
          label: const Text('Create Workout'),
          backgroundColor: AppColorPalette.primaryOrange,
        ) : null,
        orElse: () => null,
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColorPalette.primaryOrange,
                    AppColorPalette.primaryOrangeLight,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.fitness_center,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            CircularProgressIndicator(
              color: AppColorPalette.primaryOrange,
              strokeWidth: 3,
            ),
            const SizedBox(height: 16),
            Text(
              'Loading your workout...',
              style: AppTypography.displayNumbers(
                fontSize: 18,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(String error) {
    return Center(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.red.withOpacity(0.5),
                      width: 2,
                    ),
                  ),
                  child: const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 40,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'Unable to load workout',
                  style: AppTypography.displayNumbers(
                    fontSize: 20,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  error,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    ref.invalidate(currentWorkoutProvider);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColorPalette.primaryOrange,
                          AppColorPalette.primaryOrangeLight,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.refresh,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Try Again',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
  }

  Widget _buildNoWorkoutScreen() {
    return Center(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Celebration icon
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColorPalette.primaryOrange,
                          AppColorPalette.primaryOrangeLight,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: AppColorPalette.primaryOrange.withOpacity(0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.celebration,
                      color: Colors.white,
                      size: 60,
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  Text(
                    'All Caught Up!',
                    style: AppTypography.displayNumbers(
                      fontSize: 28,
                      color: Colors.white,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    'You\'ve completed all your workouts.\nGreat job staying consistent!',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // Create new workout button
                  GestureDetector(
                    onTap: () {
                      HapticFeedback.mediumImpact();
                      // TODO: Navigate to workout creation or refresh workouts
                      ref.invalidate(currentWorkoutProvider);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.add,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Check for New Workouts',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Create custom workout button
                  GestureDetector(
                    onTap: () async {
                      HapticFeedback.mediumImpact();
                      final result = await Navigator.of(context).push<bool>(
                        MaterialPageRoute(
                          builder: (context) => const CustomWorkoutCreationScreen(),
                        ),
                      );
                      
                      // Refresh workouts if a new one was created
                      if (result == true) {
                        ref.invalidate(currentWorkoutProvider);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColorPalette.primaryOrange,
                            AppColorPalette.primaryOrangeLight,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColorPalette.primaryOrange.withOpacity(0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.edit,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Create Custom Workout',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
  }
}
