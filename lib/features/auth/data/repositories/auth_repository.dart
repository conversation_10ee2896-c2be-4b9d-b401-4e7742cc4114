import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../shared/services/supabase_service.dart';
import '../../../../core/errors/app_exceptions.dart';
import '../../../../core/errors/error_handler.dart';

class AuthRepository {
  Future<void> signUp({
    required String email,
    required String password,
  }) async {
    try {
      await SupabaseService.signUp(
        email: email,
        password: password,
      );
    } catch (e) {
      final exception = ErrorHandler.handleAuthError(e);
      ErrorHandler.logError(exception, context: 'AuthRepository.signUp');
      throw exception;
    }
  }

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    try {
      await SupabaseService.signIn(
        email: email,
        password: password,
      );
    } catch (e) {
      final exception = ErrorHandler.handleAuthError(e);
      ErrorHandler.logError(exception, context: 'AuthRepository.signIn');
      throw exception;
    }
  }

  Future<void> signOut() async {
    try {
      await SupabaseService.signOut();
    } catch (e) {
      final exception = ErrorHandler.handleAuthError(e);
      ErrorHandler.logError(exception, context: 'AuthRepository.signOut');
      throw exception;
    }
  }

  User? get currentUser => SupabaseService.currentUser;

  Stream<AuthState> get authStateChanges => SupabaseService.authStateChanges;
}
