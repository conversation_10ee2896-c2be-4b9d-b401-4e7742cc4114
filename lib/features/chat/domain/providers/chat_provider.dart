import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/services/workout_chat_service.dart';
import '../../../../shared/services/supabase_service.dart';
import '../models/chat_message.dart';

/// Chat state
class ChatState {
  final List<ChatMessage> messages;
  final bool isLoading;
  final String? error;
  final bool isInitialized;
  final String? sessionId;

  const ChatState({
    this.messages = const [],
    this.isLoading = false,
    this.error,
    this.isInitialized = false,
    this.sessionId,
  });

  ChatState copyWith({
    List<ChatMessage>? messages,
    bool? isLoading,
    String? error,
    bool? isInitialized,
    String? sessionId,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isInitialized: isInitialized ?? this.isInitialized,
      sessionId: sessionId ?? this.sessionId,
    );
  }
}

/// Chat notifier
class ChatNotifier extends StateNotifier<ChatState> {
  ChatNotifier() : super(const ChatState());

  WorkoutChatService? _chatService;

  /// Initialize chat service
  Future<void> initializeChat() async {
    if (state.isInitialized) return;

    state = state.copyWith(isLoading: true);

    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      _chatService = WorkoutChatService(userId: userId);
      await _chatService!.initializeSession();

      state = state.copyWith(
        isLoading: false,
        isInitialized: true,
        error: null,
        sessionId: _chatService!.sessionId,
      );

      // Add welcome message
      _addWelcomeMessage();

      debugPrint('💬 Chat initialized for user: $userId');
    } catch (e) {
      debugPrint('❌ Failed to initialize chat: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Add welcome message
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage(
      id: 'welcome_${DateTime.now().millisecondsSinceEpoch}',
      text: "👋 Hello! I'm your AI fitness coach. I can help you with:\n\n"
            "• Workout tips and advice\n"
            "• Exercise form guidance\n"
            "• Nutrition recommendations\n"
            "• Motivation and goal setting\n\n"
            "What would you like to know about fitness today?",
      isUser: false,
      timestamp: DateTime.now(),
    );

    state = state.copyWith(
      messages: [welcomeMessage, ...state.messages],
    );
  }

  /// Send message to chat
  Future<void> sendMessage(String text) async {
    if (_chatService == null) {
      await initializeChat();
    }

    if (text.trim().isEmpty) return;

    // Add user message
    final userMessage = ChatMessage(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      text: text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );
    final loadingMessage = ChatMessage(
      id: 'loading_${DateTime.now().millisecondsSinceEpoch}',
      text: 'Coach is typing...',
      isUser: false,
      timestamp: DateTime.now(),
      isLoading: true,
    );

    state = state.copyWith(
      messages: [loadingMessage, userMessage, ...state.messages],
      error: null,
    );

    try {
      // Send to n8n
      final response = await _chatService!.sendMessage(text.trim());

      // Remove loading message and add bot response
      final updatedMessages = state.messages.where((msg) => !msg.isLoading).toList();
      final botMessage = ChatMessage(
        id: 'bot_${DateTime.now().millisecondsSinceEpoch}',
        text: response,
        isUser: false,
        timestamp: DateTime.now(),
      );

      state = state.copyWith(
        messages: [botMessage, ...updatedMessages],
      );

      debugPrint('💬 Chat exchange completed successfully');
    } catch (e) {
      debugPrint('❌ Chat message failed: $e');
      
      // Remove loading message and add error message
      final updatedMessages = state.messages.where((msg) => !msg.isLoading).toList();
      final errorMessage = ChatMessage(
        id: 'error_${DateTime.now().millisecondsSinceEpoch}',
        text: 'Sorry, I encountered an error. Please try again in a moment.',
        isUser: false,
        timestamp: DateTime.now(),
        error: e.toString(),
      );

      state = state.copyWith(
        messages: [errorMessage, ...updatedMessages],
        error: e.toString(),
      );
    }
  }

  /// Clear chat history (keeps same session)
  void clearChat() {
    state = state.copyWith(
      messages: [],
    );
    _addWelcomeMessage();
  }

  /// Start new session (clears history and creates new session ID)
  Future<void> startNewSession() async {
    if (_chatService == null) {
      await initializeChat();
      return;
    }

    state = state.copyWith(isLoading: true);

    try {
      // Start new session in service
      await _chatService!.startNewSession();
      
      // Clear UI messages
      state = state.copyWith(
        messages: [],
        isLoading: false,
        error: null,
        sessionId: _chatService!.sessionId,
      );
      
      // Add welcome message for new session
      _addWelcomeMessage();
      
      debugPrint('💬 New chat session started');
    } catch (e) {
      debugPrint('❌ Failed to start new session: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Reset chat service (for logout)
  Future<void> resetChat() async {
    if (_chatService != null) {
      await _chatService!.clearSession();
    }
    
    state = const ChatState();
    _chatService = null;
    debugPrint('💬 Chat reset');
  }

  @override
  void dispose() {
    // Don't clear session on dispose, only on explicit reset
    super.dispose();
  }
}

/// Chat provider
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier();
});