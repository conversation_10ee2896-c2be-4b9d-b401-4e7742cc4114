import 'package:flutter/material.dart';
import '../../../../core/theme/color_palette.dart';
import '../../domain/models/chat_message.dart';

class ChatBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatBubble({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisAlignment: message.isUser 
          ? MainAxisAlignment.end 
          : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!message.isUser) ...[
          // AI Coach Avatar
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColorPalette.primaryOrange,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.smart_toy,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 8),
        ],
        
        // Message bubble
        Flexible(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            decoration: BoxDecoration(
              color: _getBubbleColor(theme),
              borderRadius: _getBubbleBorderRadius(),
              border: message.error != null
                  ? Border.all(color: AppColorPalette.error.withOpacity(0.3))
                  : null,
            ),
            child: _buildMessageContent(theme),
          ),
        ),
        
        if (message.isUser) ...[
          const SizedBox(width: 8),
          // User Avatar
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              Icons.person,
              color: theme.colorScheme.onPrimary,
              size: 20,
            ),
          ),
        ],
      ],
    );
  }

  Color _getBubbleColor(ThemeData theme) {
    if (message.error != null) {
      return AppColorPalette.error.withOpacity(0.1);
    }
    
    if (message.isUser) {
      return AppColorPalette.primaryOrange;
    }
    
    return theme.colorScheme.surfaceContainer;
  }

  BorderRadius _getBubbleBorderRadius() {
    if (message.isUser) {
      return const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(4),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(20),
      );
    } else {
      return const BorderRadius.only(
        topLeft: Radius.circular(4),
        topRight: Radius.circular(20),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(20),
      );
    }
  }

  Widget _buildMessageContent(ThemeData theme) {
    if (message.isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColorPalette.primaryOrange,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Coach is typing...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          message.text,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: message.isUser 
                ? Colors.white 
                : (message.error != null 
                    ? AppColorPalette.error 
                    : theme.colorScheme.onSurface),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          _formatTimestamp(message.timestamp),
          style: theme.textTheme.bodySmall?.copyWith(
            color: message.isUser 
                ? Colors.white.withOpacity(0.7)
                : theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
            fontSize: 11,
          ),
        ),
      ],
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final diff = now.difference(timestamp);
    
    if (diff.inMinutes < 1) {
      return 'Just now';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}h ago';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }
}