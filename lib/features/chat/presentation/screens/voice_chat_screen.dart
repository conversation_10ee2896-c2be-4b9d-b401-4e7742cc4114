import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../shared/services/elevenlabs_conversational_service.dart';
import '../../../../shared/models/conversation_models.dart';

class VoiceChatScreen extends StatefulWidget {
  const VoiceChatScreen({super.key});

  @override
  State<VoiceChatScreen> createState() => _VoiceChatScreenState();
}

class _VoiceChatScreenState extends State<VoiceChatScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;

  // ElevenLabs service instance
  late ElevenLabsConversationalService _elevenLabsService;

  // State variables
  bool _isRecording = false;
  bool _isInitialized = false;
  ConversationStatus _connectionStatus = ConversationStatus.disconnected;
  ConversationMode _conversationMode = ConversationMode.idle;
  String _status = 'Tap to start talking with <PERSON>';
  final List<ChatMessage> _messages = [];
  double _vadScore = 0.0;

  @override
  void initState() {
    super.initState();
    _elevenLabsService = ElevenLabsConversationalService.instance;
    _setupAnimations();
    _initializeVoiceChat();
    _setupStreams();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _waveAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(_waveController);

    _pulseController.repeat(reverse: true);
    _waveController.repeat();
  }

  void _setupStreams() {
    // Listen to connection status changes
    _elevenLabsService.statusStream.listen((status) {
      setState(() {
        _connectionStatus = status;
        switch (status) {
          case ConversationStatus.connecting:
            _status = 'Connecting to Nathan...';
            break;
          case ConversationStatus.connected:
            _status = 'Connected! Ready to talk with Nathan 🎤';
            _isInitialized = true;
            break;
          case ConversationStatus.disconnected:
            _status = 'Tap to start talking with Nathan';
            _isInitialized = false;
            break;
          case ConversationStatus.error:
            _status = 'Connection error. Tap to retry.';
            _isInitialized = false;
            break;
        }
      });
    });

    // Listen to conversation mode changes
    _elevenLabsService.modeStream.listen((mode) {
      setState(() {
        _conversationMode = mode;
        switch (mode) {
          case ConversationMode.listening:
            _status = 'Listening... speak now';
            break;
          case ConversationMode.speaking:
            _status = 'Nathan is speaking...';
            break;
          case ConversationMode.processing:
            _status = 'Processing your message...';
            break;
          case ConversationMode.idle:
            if (_connectionStatus == ConversationStatus.connected) {
              _status = 'Ready! Tap to talk with Nathan 🎤';
            }
            break;
        }
      });
    });

    // Listen to transcriptions
    _elevenLabsService.transcriptionStream.listen((transcription) {
      setState(() {
        _messages.add(ChatMessage(
          text: transcription,
          isUser: true,
          timestamp: DateTime.now(),
        ));
      });
    });

    // Listen to agent responses
    _elevenLabsService.responseStream.listen((response) {
      setState(() {
        _messages.add(ChatMessage(
          text: response,
          isUser: false,
          timestamp: DateTime.now(),
        ));
      });
    });

    // Listen to recording status
    _elevenLabsService.recordingStatusStream.listen((isRecording) {
      setState(() {
        _isRecording = isRecording;
      });
    });

    // Listen to VAD scores for voice visualization
    _elevenLabsService.vadScoreStream.listen((vadScore) {
      setState(() {
        _vadScore = vadScore;
      });
    });

    // Listen to errors
    _elevenLabsService.errorStream.listen((error) {
      setState(() {
        _status = 'Error: $error';
      });
      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    });
  }

  Future<void> _initializeVoiceChat() async {
    try {
      setState(() {
        _status = 'Initializing voice chat...';
      });

      await _elevenLabsService.initialize();

      // Start the conversation automatically after initialization
      await _elevenLabsService.startConversation();

    } catch (e) {
      setState(() {
        _status = 'Failed to initialize: $e';
      });
    }
  }

  Future<void> _toggleRecording() async {
    if (!_isInitialized || _connectionStatus != ConversationStatus.connected) return;

    try {
      if (_isRecording) {
        await _elevenLabsService.stopRecording();
      } else {
        await _elevenLabsService.startRecording();
      }
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  void _clearChat() {
    setState(() {
      _messages.clear();
      _status = 'Chat cleared. Ready to talk!';
    });
    // Note: ElevenLabs maintains conversation context automatically
    // No need to explicitly clear chat history
  }

  void _showInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Voice Chat with Nathan'),
        content: const Text(
                                  'This feature provides real-time voice conversations with your AI fitness coach Nathan.\n\n'
          'Features:\n'
          '• Real-time speech recognition\n'
          '• Gemini AI responses\n'
          '• Text-to-speech output\n'
          '• Continuous conversation memory\n\n'
          'Tap the microphone to start talking!'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    // End the conversation when leaving the screen
    _elevenLabsService.endConversation();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: AppBar(
        title: const Text(
          'Voice Chat',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline, color: Colors.white),
            onPressed: _showInfo,
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _clearChat,
          ),
        ],
      ),
      body: Column(
        children: [
          // Chat Messages Area
          Expanded(
            flex: 3,
            child: _messages.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.chat_bubble_outline,
                          size: 64,
                          color: Colors.white.withOpacity(0.3),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Start a conversation with Nathan!',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.6),
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      final message = _messages[index];
                      return ChatBubble(message: message);
                    },
                  ),
          ),

          // Voice Visualizer
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Animated Voice Visualizer
                  AnimatedBuilder(
                    animation: Listenable.merge([_pulseAnimation, _waveAnimation]),
                    builder: (context, child) {
                      return CustomPaint(
                        size: const Size(200, 200),
                        painter: VoiceVisualizerPainter(
                          isRecording: _isRecording,
                          pulseValue: _pulseAnimation.value,
                          waveValue: _waveAnimation.value,
                          vadScore: _vadScore,
                          conversationMode: _conversationMode,
                        ),
                      );
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Status Text
                  Text(
                    _status,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Control Panel
          Container(
            padding: const EdgeInsets.all(24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Test Text Input
                ElevatedButton.icon(
                  onPressed: _isInitialized ? () => _showTextInput() : null,
                  icon: const Icon(Icons.keyboard),
                  label: const Text('Type'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[800],
                    foregroundColor: Colors.white,
                  ),
                ),
                
                // Main Record Button
                GestureDetector(
                  onTap: _toggleRecording,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _isRecording ? Colors.red : Colors.orange,
                      boxShadow: [
                        BoxShadow(
                          color: (_isRecording ? Colors.red : Colors.orange)
                              .withOpacity(0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: Icon(
                      _isRecording ? Icons.stop : Icons.mic,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
                
                // Settings
                ElevatedButton.icon(
                  onPressed: _showInfo,
                  icon: const Icon(Icons.settings),
                  label: const Text('Info'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[800],
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showTextInput() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('Send Text Message'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: 'Type your message to Nathan...',
            ),
            maxLines: 3,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (controller.text.trim().isNotEmpty) {
                  _elevenLabsService.sendTextMessage(controller.text.trim());
                  Navigator.pop(context);
                }
              },
              child: const Text('Send'),
            ),
          ],
        );
      },
    );
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatBubble({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!message.isUser)
            CircleAvatar(
              backgroundColor: Colors.orange,
              radius: 16,
              child: const Text('N', style: TextStyle(color: Colors.white)),
            ),
          if (!message.isUser) const SizedBox(width: 8),
          
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isUser ? Colors.orange : Colors.grey[800],
                borderRadius: BorderRadius.circular(18),
              ),
              child: Text(
                message.text,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          
          if (message.isUser) const SizedBox(width: 8),
          if (message.isUser)
            CircleAvatar(
              backgroundColor: Colors.blue,
              radius: 16,
              child: const Text('U', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
    );
  }
}

class VoiceVisualizerPainter extends CustomPainter {
  final bool isRecording;
  final double pulseValue;
  final double waveValue;
  final double vadScore;
  final ConversationMode conversationMode;

  VoiceVisualizerPainter({
    required this.isRecording,
    required this.pulseValue,
    required this.waveValue,
    this.vadScore = 0.0,
    this.conversationMode = ConversationMode.idle,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width / 4;

    // Background circle
    final backgroundPaint = Paint()
      ..color = Colors.grey[800]!.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, baseRadius, backgroundPaint);

    // Choose colors and behavior based on conversation mode
    Color primaryColor;
    double intensity = 1.0;

    switch (conversationMode) {
      case ConversationMode.listening:
        primaryColor = Colors.red;
        intensity = 1.0 + (vadScore * 0.5); // Use VAD score for intensity
        break;
      case ConversationMode.speaking:
        primaryColor = Colors.green;
        intensity = 1.2;
        break;
      case ConversationMode.processing:
        primaryColor = Colors.blue;
        intensity = 1.1;
        break;
      case ConversationMode.idle:
        primaryColor = Colors.orange;
        intensity = 1.0;
        break;
    }

    if (isRecording || conversationMode == ConversationMode.listening) {
      // Animated pulse rings with VAD-based intensity
      for (int i = 0; i < 3; i++) {
        final ringPaint = Paint()
          ..color = primaryColor.withValues(alpha: (0.3 - (i * 0.1)) * intensity)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2;

        final ringRadius = baseRadius + (pulseValue * 30 * intensity) + (i * 20);
        canvas.drawCircle(center, ringRadius, ringPaint);
      }

      // Center recording indicator
      final recordPaint = Paint()
        ..color = primaryColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, baseRadius * 0.6 * intensity, recordPaint);

      // Mic icon representation
      final micPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, baseRadius * 0.3, micPaint);
    } else if (conversationMode == ConversationMode.speaking) {
      // Speaking animation with wave-like effect
      for (int i = 0; i < 5; i++) {
        final wavePaint = Paint()
          ..color = primaryColor.withValues(alpha: 0.4 - (i * 0.05))
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3;

        final waveRadius = baseRadius + (math.sin(waveValue + i) * 20) + (i * 10);
        canvas.drawCircle(center, waveRadius, wavePaint);
      }

      // Center speaking indicator
      final speakPaint = Paint()
        ..color = primaryColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, baseRadius * 0.7, speakPaint);

      // Speaker icon representation
      final iconPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, baseRadius * 0.35, iconPaint);
    } else {
      // Idle state with gentle glow
      final glowPaint = Paint()
        ..color = primaryColor.withValues(alpha: 0.5)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, baseRadius * pulseValue, glowPaint);

      // Center microphone
      final micPaint = Paint()
        ..color = primaryColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, baseRadius * 0.6, micPaint);

      // Mic icon
      final iconPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, baseRadius * 0.25, iconPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
} 