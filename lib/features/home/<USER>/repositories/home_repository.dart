import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/home_data.dart';

class HomeRepository {
  final SupabaseClient _supabase;

  HomeRepository(this._supabase);

  Future<HomeData> getHomeData(String userId) async {
    try {
      // Fetch user stats
      final userStats = await _getUserStats(userId);
      
      // Fetch recent workouts
      final recentWorkouts = await _getRecentWorkouts(userId);
      
      // Fetch recommended workouts
      final recommendedWorkouts = await _getRecommendedWorkouts(userId);
      
      // Fetch today's progress
      final todayProgress = await _getTodayProgress(userId);
      
      // Calculate streak and points
      final streakData = await _getStreakAndPoints(userId);
      
      return HomeData(
        userStats: userStats,
        recentWorkouts: recentWorkouts,
        recommendedWorkouts: recommendedWorkouts,
        todayProgress: todayProgress,
        currentStreak: streakData['streak'] ?? 0,
        totalPoints: streakData['points'] ?? 0,
      );
    } catch (e) {
      throw Exception('Failed to load home data: $e');
    }
  }

  Future<UserStats> _getUserStats(String userId) async {
    final response = await _supabase
        .from('completed_workouts')
        .select()
        .eq('user_id', userId);
    
    final workouts = List<Map<String, dynamic>>.from(response);
    
    // Calculate stats
    final totalWorkouts = workouts.length;
    final totalMinutes = workouts.fold<int>(
      0, (sum, workout) => sum + (workout['duration'] as int? ?? 0));
    final totalCalories = workouts.fold<int>(
      0, (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0));
    
    // Calculate this week's workouts
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final workoutsThisWeek = workouts.where((workout) {
      final date = DateTime.parse(workout['date_completed']);
      return date.isAfter(weekStart);
    }).length;
    
    // Calculate this month's workouts
    final monthStart = DateTime(now.year, now.month, 1);
    final workoutsThisMonth = workouts.where((workout) {
      final date = DateTime.parse(workout['date_completed']);
      return date.isAfter(monthStart);
    }).length;
    
    return UserStats(
      totalWorkouts: totalWorkouts,
      totalMinutes: totalMinutes,
      totalCalories: totalCalories,
      avgWorkoutDuration: totalWorkouts > 0 ? totalMinutes / totalWorkouts : 0,
      workoutsThisWeek: workoutsThisWeek,
      workoutsThisMonth: workoutsThisMonth,
    );
  }

  Future<List<WorkoutSummary>> _getRecentWorkouts(String userId) async {
    final response = await _supabase
        .from('workouts')
        .select('''
          *,
          workout_exercises(count),
          completed_workouts(date_completed)
        ''')
        .eq('user_id', userId)
        .order('created_at', ascending: false)
        .limit(5);
    
    return (response as List).map((workout) {
      final exerciseCount = workout['workout_exercises']?[0]?['count'] ?? 0;
      final completions = workout['completed_workouts'] as List? ?? [];
      final lastCompleted = completions.isNotEmpty 
          ? DateTime.parse(completions.first['date_completed'])
          : null;
      
      return WorkoutSummary(
        id: workout['id'],
        name: workout['name'],
        category: _categorizeWorkout(workout['name']),
        duration: 30, // Default duration, should be calculated
        difficulty: 'Intermediate', // Should be determined from workout
        exercises: exerciseCount,
        calories: 250, // Should be calculated
        rating: 4.5, // Should come from user ratings
        equipment: [], // Should be extracted from exercises
        lastCompleted: lastCompleted,
        isCompleted: workout['is_completed'] ?? false,
      );
    }).toList();
  }

  Future<List<WorkoutSummary>> _getRecommendedWorkouts(String userId) async {
    // Get user profile to understand preferences
    final profile = await _supabase
        .from('profiles')
        .select()
        .eq('id', userId)
        .single();
    
    final fitnessGoals = List<String>.from(profile['fitness_goals_array'] ?? []);
    final equipment = List<String>.from(profile['equipment'] ?? []);
    final fitnessLevel = profile['fitness_level'] ?? 'Intermediate';
    
    // Query database for personalized workout recommendations
    // based on user's fitness goals, equipment, and level
    final workoutQuery = SupabaseService.client
        .from('workouts')
        .select('id, name, category, duration, difficulty, exercises, calories, rating, equipment')
        .eq('is_active', true)
        .inFilter('difficulty', [fitnessLevel, 'Beginner']) // Include beginner as baseline
        .order('rating', ascending: false)
        .limit(10);

    final workoutResponse = await workoutQuery;
    
    if (workoutResponse.isEmpty) {
      return [];
    }

    return workoutResponse.map<WorkoutSummary>((workout) {
      return WorkoutSummary(
        id: workout['id'].toString(),
        name: workout['name'] ?? 'Unknown Workout',
        category: workout['category'] ?? 'General',
        duration: workout['duration'] ?? 30,
        difficulty: workout['difficulty'] ?? fitnessLevel,
        exercises: workout['exercises'] ?? 8,
        calories: workout['calories'] ?? 200,
        rating: (workout['rating'] as num?)?.toDouble() ?? 4.0,
        equipment: List<String>.from(workout['equipment'] ?? ['None']),
      );
    }).toList();
  }

  Future<TodayProgress> _getTodayProgress(String userId) async {
    // Fetch user's daily progress from database
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    final progressResponse = await SupabaseService.client
        .from('daily_progress')
        .select()
        .eq('user_id', userId)
        .eq('date', today)
        .maybeSingle();
    
    if (progressResponse == null) {
      // Return empty progress if no data exists for today
      return const TodayProgress(
        calories: 0,
        caloriesGoal: 1300,
        steps: 0,
        stepsGoal: 10000,
        activeMinutes: 0,
        activeMinutesGoal: 60,
        heartRate: 0,
        waterIntake: 0,
        waterIntakeGoal: 8,
      );
    }
    
    return TodayProgress(
      calories: progressResponse['calories'] ?? 0,
      caloriesGoal: progressResponse['calories_goal'] ?? 1300,
      steps: progressResponse['steps'] ?? 0,
      stepsGoal: progressResponse['steps_goal'] ?? 10000,
      activeMinutes: progressResponse['active_minutes'] ?? 0,
      activeMinutesGoal: progressResponse['active_minutes_goal'] ?? 60,
      heartRate: progressResponse['heart_rate'] ?? 0,
      waterIntake: progressResponse['water_intake'] ?? 0,
      waterIntakeGoal: progressResponse['water_intake_goal'] ?? 8,
    );
  }

  Future<Map<String, int>> _getStreakAndPoints(String userId) async {
    final response = await _supabase
        .from('completed_workouts')
        .select('date_completed')
        .eq('user_id', userId)
        .order('date_completed', ascending: false);
    
    final completions = List<Map<String, dynamic>>.from(response);
    
    // Calculate streak
    int streak = 0;
    if (completions.isNotEmpty) {
      DateTime? lastDate;
      for (final completion in completions) {
        final date = DateTime.parse(completion['date_completed']);
        final dateOnly = DateTime(date.year, date.month, date.day);
        
        if (lastDate == null) {
          // First workout
          final today = DateTime.now();
          final todayOnly = DateTime(today.year, today.month, today.day);
          if (dateOnly == todayOnly || 
              dateOnly == todayOnly.subtract(const Duration(days: 1))) {
            streak = 1;
            lastDate = dateOnly;
          } else {
            break;
          }
        } else {
          // Check if consecutive day
          if (lastDate.subtract(const Duration(days: 1)) == dateOnly) {
            streak++;
            lastDate = dateOnly;
          } else {
            break;
          }
        }
      }
    }
    
    // Calculate points (10 points per workout + 50 bonus per week streak)
    final points = completions.length * 10 + (streak ~/ 7) * 50;
    
    return {'streak': streak, 'points': points};
  }

  String _categorizeWorkout(String workoutName) {
    final name = workoutName.toLowerCase();
    if (name.contains('strength') || name.contains('weight')) return 'Strength';
    if (name.contains('cardio') || name.contains('run')) return 'Cardio';
    if (name.contains('hiit')) return 'HIIT';
    if (name.contains('yoga') || name.contains('stretch')) return 'Yoga';
    return 'General';
  }
}