import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/onboarding_state.dart';
import '../../data/repositories/onboarding_repository.dart';
import '../../../profile/domain/models/user_profile.dart';

/// Onboarding state notifier
class OnboardingNotifier extends StateNotifier<OnboardingState> {
  OnboardingNotifier(this._repository) : super(const OnboardingState());

  final OnboardingRepository _repository;

  /// Navigate to next page
  void nextPage() {
    if (state.currentPage < state.totalPages - 1 && state.canProceedFromCurrentPage) {
      state = state.copyWith(currentPage: state.currentPage + 1);
    }
  }

  /// Navigate to previous page
  void previousPage() {
    if (state.currentPage > 0) {
      state = state.copyWith(currentPage: state.currentPage - 1);
    }
  }

  /// Jump to specific page
  void goToPage(int page) {
    if (page >= 0 && page < state.totalPages) {
      state = state.copyWith(currentPage: page);
    }
  }

  /// Update personal information (Page 1)
  void updatePersonalInfo({
    String? name,
    String? gender,
    int? age,
    double? height,
    String? heightUnit,
    double? weight,
    String? weightUnit,
  }) {
    state = state.copyWith(
      name: name ?? state.name,
      gender: gender ?? state.gender,
      age: age ?? state.age,
      height: height ?? state.height,
      heightUnit: heightUnit ?? state.heightUnit,
      weight: weight ?? state.weight,
      weightUnit: weightUnit ?? state.weightUnit,
    );
  }

  /// Update fitness goals (Page 2)
  void updateFitnessGoals({
    List<String>? selectedGoals,
    String? primaryGoal,
    String? sportActivity,
    String? additionalHealthInfo,
  }) {
    state = state.copyWith(
      selectedGoals: selectedGoals ?? state.selectedGoals,
      primaryGoal: primaryGoal ?? state.primaryGoal,
      sportActivity: sportActivity ?? state.sportActivity,
      additionalHealthInfo: additionalHealthInfo ?? state.additionalHealthInfo,
    );
  }

  /// Add or remove fitness goal
  void toggleFitnessGoal(String goal) {
    final currentGoals = List<String>.from(state.selectedGoals);
    if (currentGoals.contains(goal)) {
      currentGoals.remove(goal);
    } else {
      currentGoals.add(goal);
    }
    
    state = state.copyWith(
      selectedGoals: currentGoals,
      primaryGoal: currentGoals.isNotEmpty ? currentGoals.first : null,
    );
  }

  /// Reorder fitness goals
  void reorderFitnessGoals(List<String> newOrder) {
    state = state.copyWith(
      selectedGoals: newOrder,
      primaryGoal: newOrder.isNotEmpty ? newOrder.first : null,
    );
  }

  /// Update fitness levels (Page 3)
  void updateFitnessLevels({
    int? cardioLevel,
    int? weightliftingLevel,
    List<String>? exercisesToAvoid,
  }) {
    state = state.copyWith(
      cardioLevel: cardioLevel ?? state.cardioLevel,
      weightliftingLevel: weightliftingLevel ?? state.weightliftingLevel,
      exercisesToAvoid: exercisesToAvoid ?? state.exercisesToAvoid,
    );
  }

  /// Add or remove exercise to avoid
  void toggleExerciseToAvoid(String exercise) {
    final currentExercises = List<String>.from(state.exercisesToAvoid);
    if (currentExercises.contains(exercise)) {
      currentExercises.remove(exercise);
    } else {
      currentExercises.add(exercise);
    }
    state = state.copyWith(exercisesToAvoid: currentExercises);
  }

  /// Update workout environment (Page 4)
  void updateWorkoutEnvironment({
    List<String>? workoutEnvironments,
    List<String>? availableEquipment,
  }) {
    state = state.copyWith(
      workoutEnvironments: workoutEnvironments ?? state.workoutEnvironments,
      availableEquipment: availableEquipment ?? state.availableEquipment,
    );
  }

  /// Toggle workout environment
  void toggleWorkoutEnvironment(String environment) {
    final currentEnvironments = List<String>.from(state.workoutEnvironments);
    if (currentEnvironments.contains(environment)) {
      currentEnvironments.remove(environment);
    } else {
      currentEnvironments.add(environment);
    }
    state = state.copyWith(workoutEnvironments: currentEnvironments);
  }

  /// Toggle equipment
  void toggleEquipment(String equipment) {
    final currentEquipment = List<String>.from(state.availableEquipment);
    if (currentEquipment.contains(equipment)) {
      currentEquipment.remove(equipment);
    } else {
      currentEquipment.add(equipment);
    }
    state = state.copyWith(availableEquipment: currentEquipment);
  }

  /// Update workout schedule (Page 5)
  void updateWorkoutSchedule({
    int? workoutFrequency,
    String? sessionDuration,
    List<String>? preferredDays,
  }) {
    state = state.copyWith(
      workoutFrequency: workoutFrequency ?? state.workoutFrequency,
      sessionDuration: sessionDuration ?? state.sessionDuration,
      preferredDays: preferredDays ?? state.preferredDays,
    );
  }

  /// Toggle preferred workout day
  void togglePreferredDay(String day) {
    final currentDays = List<String>.from(state.preferredDays);
    if (currentDays.contains(day)) {
      currentDays.remove(day);
    } else {
      currentDays.add(day);
    }
    state = state.copyWith(preferredDays: currentDays);
  }

  /// Update additional information (Page 6)
  void updateAdditionalInfo({
    String? additionalNotes,
    List<String>? healthConditions,
    List<String>? dietaryRestrictions,
  }) {
    state = state.copyWith(
      additionalNotes: additionalNotes ?? state.additionalNotes,
      healthConditions: healthConditions ?? state.healthConditions,
      dietaryRestrictions: dietaryRestrictions ?? state.dietaryRestrictions,
    );
  }

  /// Toggle health condition
  void toggleHealthCondition(String condition) {
    final currentConditions = List<String>.from(state.healthConditions);
    if (currentConditions.contains(condition)) {
      currentConditions.remove(condition);
    } else {
      currentConditions.add(condition);
    }
    state = state.copyWith(healthConditions: currentConditions);
  }

  /// Toggle dietary restriction
  void toggleDietaryRestriction(String restriction) {
    final currentRestrictions = List<String>.from(state.dietaryRestrictions);
    if (currentRestrictions.contains(restriction)) {
      currentRestrictions.remove(restriction);
    } else {
      currentRestrictions.add(restriction);
    }
    state = state.copyWith(dietaryRestrictions: currentRestrictions);
  }

  /// Complete onboarding and save profile
  Future<void> completeOnboarding(String userId) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final profile = state.toUserProfile(userId);
      await _repository.saveUserProfile(profile);
      
      state = state.copyWith(
        isLoading: false,
        isCompleted: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Reset onboarding state
  void reset() {
    state = const OnboardingState();
  }

  /// Load existing profile data
  Future<void> loadExistingProfile(String userId) async {
    state = state.copyWith(isLoading: true);
    
    try {
      final profile = await _repository.getUserProfile(userId);
      if (profile != null) {
        _populateFromProfile(profile);
      }
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Populate state from existing profile
  void _populateFromProfile(UserProfile profile) {
    state = state.copyWith(
      name: profile.displayName,
      gender: profile.gender,
      age: profile.age,
      height: profile.height,
      heightUnit: profile.heightUnit,
      weight: profile.weight,
      weightUnit: profile.weightUnit,
      selectedGoals: profile.fitnessGoalsArray,
      primaryGoal: profile.fitnessGoalPrimary,
      sportActivity: profile.sportOfChoice,
      additionalHealthInfo: profile.additionalHealthInfo,
      cardioLevel: profile.cardioFitnessLevel,
      weightliftingLevel: profile.weightliftingFitnessLevel,
      exercisesToAvoid: profile.exercisesToAvoid,
      workoutEnvironments: profile.workoutEnvironment,
      availableEquipment: profile.equipment,
      workoutFrequency: profile.workoutFrequencyDays,
      sessionDuration: profile.preferredWorkoutDuration,
      preferredDays: profile.workoutDays,
      additionalNotes: profile.additionalNotes,
      healthConditions: profile.healthConditions,
      dietaryRestrictions: profile.dietaryRestrictions,
      isCompleted: profile.onboardingCompleted,
    );
  }
}

/// Onboarding provider
final onboardingProvider = StateNotifierProvider.autoDispose<OnboardingNotifier, OnboardingState>((ref) {
  final repository = ref.watch(onboardingRepositoryProvider);
  return OnboardingNotifier(repository);
});

/// Repository provider
final onboardingRepositoryProvider = Provider<OnboardingRepository>((ref) {
  return OnboardingRepository();
});
