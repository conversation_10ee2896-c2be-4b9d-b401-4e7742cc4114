import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../domain/providers/dashboard_provider.dart';
import '../widgets/openfit_hero_workout_card.dart';
import '../widgets/openfit_greeting_header.dart';
import '../widgets/openfit_quick_stats.dart';

/// OpenFit Home Screen - Daily workout hub with hero workout card
class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(dashboardDataProvider);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(dashboardDataProvider);
        },
        color: AppColorPalette.primaryOrange,
        backgroundColor: AppColorPalette.elevation1,
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // Greeting header with profile avatar
            SliverToBoxAdapter(
              child: SafeArea(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: const OpenFitGreetingHeader(),
                ),
              ),
            ),

            // Hero workout card
            SliverToBoxAdapter(
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.lg,
                      vertical: AppSpacing.md,
                    ),
                    child: dashboardState.when(
                      data: (data) {
                        print('📊 Dashboard data loaded: workout = ${data.todayWorkout?.name}');
                        if (data.todayWorkout == null) {
                          return _buildEmptyHeroCard();
                        }
                        return OpenFitHeroWorkoutCard(
                          workout: data.todayWorkout,
                          onStartWorkout: _handleStartWorkout,
                        );
                      },
                      loading: () {
                        print('⏳ Dashboard loading...');
                        return _buildLoadingHeroCard();
                      },
                      error: (error, stack) {
                        print('❌ Dashboard error: $error');
                        return _buildErrorHeroCard();
                      },
                    ),
                  ),
                ),
              ),
            ),

            // Quick stats section
            SliverToBoxAdapter(
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.lg,
                    ),
                    child: dashboardState.when(
                      data: (data) => OpenFitQuickStats(
                        userStats: data.userStats,
                      ),
                      loading: () => _buildLoadingStats(),
                      error: (error, stack) => const SizedBox.shrink(),
                    ),
                  ),
                ),
              ),
            ),

            // Bottom padding for navigation
            SliverToBoxAdapter(
              child: const SizedBox(height: AppSpacing.xxl * 2),
            ),
          ],
        ),
      ),
    );
  }

  void _handleStartWorkout() async {
    print('🔥 START WORKOUT BUTTON PRESSED!');

    // Step 1: Immediate visual feedback (0-100ms)
    HapticFeedback.mediumImpact();

    try {
      print('🚀 Navigating to workout loading screen...');
      // Step 2: Navigate to loading screen which handles the complete flow
      context.go('/workout-loading');

    } catch (e) {
      print('❌ Error in _handleStartWorkout: $e');
      // Show error dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: AppColorPalette.elevation1,
            title: Text(
              'Error Starting Workout',
              style: TextStyle(color: Colors.white),
            ),
            content: Text(
              e.toString(),
              style: TextStyle(color: Colors.white.withOpacity(0.8)),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'OK',
                  style: TextStyle(color: AppColorPalette.primaryOrange),
                ),
              ),
            ],
          ),
        );
      }
    }
  }

  Widget _buildLoadingHeroCard() {
    return GlassMorphismCard(
      height: 260,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppColorPalette.primaryOrange,
              strokeWidth: 3,
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'Loading your workout...',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorHeroCard() {
    return GlassMorphismCard(
      height: 260,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppColorPalette.error,
              size: 48,
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'Unable to load workout',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Pull down to refresh',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingStats() {
    return Row(
      children: List.generate(3, (index) {
        return Expanded(
          child: Container(
            margin: EdgeInsets.only(
              right: index < 2 ? AppSpacing.md : 0,
            ),
            child: GlassMorphismCard(
              height: 80,
              child: Center(
                child: CircularProgressIndicator(
                  color: AppColorPalette.primaryOrange,
                  strokeWidth: 2,
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildEmptyHeroCard() {
    return GlassMorphismCard(
      height: 260,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 72,
              height: 72,
              decoration: BoxDecoration(
                gradient: AppColorPalette.primaryGradient,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.celebration,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              'All Caught Up!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'You\'ve completed all your workouts',
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Pull down to refresh',
              style: TextStyle(
                color: Colors.white.withOpacity(0.6),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
