import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'features/auth/presentation/screens/sign_in_screen.dart';
import 'features/auth/presentation/screens/sign_up_screen.dart';
import 'features/main/presentation/screens/main_screen.dart';
import 'features/onboarding/presentation/screens/onboarding_screen.dart';
import 'features/auth/domain/providers/auth_provider.dart';
import 'features/onboarding/data/repositories/onboarding_repository.dart';
import 'features/workout/presentation/screens/workout_loading_screen.dart';
import 'core/debug/debug_config.dart';
import 'features/workout/presentation/screens/workout_countdown_screen.dart';
import 'features/workout/presentation/screens/pre_workout_screen.dart';
import 'features/workout/presentation/screens/active_workout_screen.dart';
import 'features/workout/presentation/screens/rest_screen.dart';
import 'features/workout/presentation/screens/workout_details_screen.dart';
import 'features/workout/presentation/screens/workout_completion_screen.dart';
import 'features/workout/domain/models/workout_session.dart';
import 'features/dashboard/domain/models/today_workout.dart';
import 'shared/services/supabase_service.dart';
import 'shared/widgets/error_screen.dart';
import 'core/theme/app_theme.dart';
import 'core/theme/theme_data.dart';
import 'core/theme/providers/theme_provider.dart';
import 'core/accessibility/accessibility_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables (handle missing file gracefully)
  try {
    await dotenv.load(fileName: ".env");
  } catch (e) {
    debugPrint('⚠️ Warning: Could not load .env file: $e');
    debugPrint('💡 Make sure to create a .env file with your ElevenLabs API key');
  }

  await SupabaseService.initialize();

  // Initialize SharedPreferences for theme persistence
  final sharedPreferences = await SharedPreferences.getInstance();

  // Initialize accessibility service
  await AccessibilityService.instance.initialize();

  // Initialize debug configuration
  DebugConfig.initialize();

  runApp(
    ProviderScope(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(sharedPreferences),
      ],
      child: const MyApp(),
    ),
  );
}

final goRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);

  return GoRouter(
    initialLocation: '/',
    redirect: (context, state) async {
      final isAuthenticated = authState.valueOrNull?.session != null;
      final isAuthRoute = state.matchedLocation == '/signin' || state.matchedLocation == '/signup';
      final isOnboardingRoute = state.matchedLocation == '/onboarding';

      debugPrint('🔄 Router redirect: ${state.matchedLocation}, authenticated: $isAuthenticated');

      if (!isAuthenticated && !isAuthRoute) {
        debugPrint('🔄 Redirecting to signin');
        return '/signin';
      }

      if (isAuthenticated && isAuthRoute) {
        // Check if user has completed onboarding
        final user = authState.valueOrNull?.session?.user;
        if (user != null) {
          final repository = OnboardingRepository();
          final hasCompleted = await repository.hasCompletedOnboarding(user.id);
          return hasCompleted ? '/' : '/onboarding';
        }
        return '/onboarding';
      }

      if (isAuthenticated && !isOnboardingRoute) {
        // Check if user needs onboarding
        final user = authState.valueOrNull?.session?.user;
        if (user != null) {
          final repository = OnboardingRepository();
          final hasCompleted = await repository.hasCompletedOnboarding(user.id);
          if (!hasCompleted) {
            return '/onboarding';
          }
        }
      }

      return null;
    },
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const MainScreen(),
      ),
      GoRoute(
        path: '/signin',
        builder: (context, state) => const SignInScreen(),
      ),
      GoRoute(
        path: '/signup',
        builder: (context, state) => const SignUpScreen(),
      ),
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      // Workout flow routes
      GoRoute(
        path: '/workout-loading',
        builder: (context, state) {
          final workoutId = state.extra as String?;
          return WorkoutLoadingScreen(workoutId: workoutId);
        },
      ),
      GoRoute(
        path: '/workout-countdown',
        builder: (context, state) {
          final workoutSession = state.extra;
          if (workoutSession is! WorkoutSession) {
            return const ErrorScreen(
              message: 'Invalid workout session data for countdown',
              details: 'Expected WorkoutSession information is missing or invalid for the countdown screen.',
            );
          }
          return WorkoutCountdownScreen(workoutSession: workoutSession);
        },
      ),
      GoRoute(
        path: '/pre-workout',
        builder: (context, state) {
          final workout = state.extra;
          if (workout is! WorkoutSession) {
            return const ErrorScreen(
              message: 'Invalid workout session',
              details: 'Expected workout session data is missing or invalid.',
            );
          }
          return PreWorkoutScreen(workout: workout);
        },
      ),
      GoRoute(
        path: '/active-workout',
        builder: (context, state) {
          final workout = state.extra;
          if (workout is! WorkoutSession) {
            return const ErrorScreen(
              message: 'Invalid workout session',
              details: 'Expected workout session data is missing or invalid.',
            );
          }
          return ActiveWorkoutScreen(workout: workout);
        },
      ),
      GoRoute(
        path: '/rest-screen',
        builder: (context, state) {
          final args = state.extra;
          if (args is! Map<String, dynamic>) {
            return const ErrorScreen(
              message: 'Invalid rest screen parameters',
              details: 'Expected rest screen arguments are missing or invalid.',
            );
          }
          
          final duration = args['duration'];
          if (duration is! Duration) {
            return const ErrorScreen(
              message: 'Invalid rest duration',
              details: 'Rest duration parameter is missing or invalid.',
            );
          }
          
          return RestScreen(
            restDuration: duration,
            isLastSet: args['isLastSet'] as bool? ?? false,
            nextExercise: args['nextExercise'] as WorkoutExercise?,
            onRestComplete: args['onRestComplete'] as VoidCallback?,
            onSkip: args['onSkip'] as VoidCallback?,
          );
        },
      ),
      GoRoute(
        path: '/workout-details',
        builder: (context, state) {
          final workout = state.extra;
          if (workout is! TodayWorkout) {
            return const ErrorScreen(
              message: 'Workout not found',
              details: 'The requested workout could not be loaded.',
            );
          }
          return WorkoutDetailsScreen(workout: workout);
        },
      ),
      GoRoute(
        path: '/workout-completion',
        builder: (context, state) {
          final workout = state.extra;
          if (workout is! WorkoutSession) {
            return const ErrorScreen(
              message: 'Invalid workout completion data',
              details: 'Expected workout session data is missing or invalid.',
            );
          }
          return WorkoutCompletionScreen(workout: workout);
        },
      ),
    ],
  );
});

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(goRouterProvider);
    final themeData = ref.watch(themeProvider);

    return MaterialApp.router(
      title: 'Fitness App',
      debugShowCheckedModeBanner: false,
      routerConfig: router,
      themeMode: themeData.mode.themeMode,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      // Custom builder to handle debug overflow indicators and responsive design
      builder: (context, child) {
        // Configure error widget builder for debug mode
        if (DebugConfig.isDebugMode) {
          ErrorWidget.builder = DebugConfig.errorWidgetBuilder;
        }

        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            // Clamp text scale factor for better UI consistency
            textScaleFactor: MediaQuery.of(context).textScaleFactor.clamp(0.8, 1.2),
          ),
          child: child!,
        );
      },
    );
  }
}
