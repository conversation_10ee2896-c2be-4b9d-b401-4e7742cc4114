---
description:
globs:
alwaysApply: false
---
# Project Structure Guide

This rule outlines the high-level layout of the **fitness_app** Flutter project so you can quickly locate files and understand their purpose.

## Entry point
- The application starts from [lib/main.dart](mdc:lib/main.dart). This sets up global providers, themes, and the root `MaterialApp`.

## Core utilities
- Common, non-feature-specific code lives under [lib/core](mdc:lib/core).  
  • Configuration/constants → [lib/core/constants](mdc:lib/core/constants)  
  • Theming → [lib/core/theme](mdc:lib/core/theme)  
  • Animation helpers, debugging, performance, etc. reside in their respective sub-folders.

## Feature-first structure
Each user-facing domain is isolated inside **lib/features** following Clean Architecture layers:

```
lib/
  features/
    <feature>/
      data/       // API, repositories, local storage adapters
      domain/     // models, business logic, providers/services
      presentation/ // screens, widgets, view-state classes
```

Examples:
- Authentication feature root → [lib/features/auth](mdc:lib/features/auth)  
- Dashboard feature root → [lib/features/dashboard](mdc:lib/features/dashboard)

When adding a new feature, mirror this folder pattern to keep the codebase consistent.

## Shared components
- Reusable widgets and cross-cutting services that don't belong to a single feature are placed in [lib/shared](mdc:lib/shared).

## Testing
- All tests are collated under the top-level [test](mdc:test) directory, mirroring the `lib` structure where possible.

## Platform code
- Native Android code sits in [android/app/src/main](mdc:android/app/src/main) while iOS resides in [ios/Runner](mdc:ios/Runner). You rarely need to modify these directly unless integrating platform-specific services.

---
Follow this guide to keep navigation intuitive and contributions aligned with the existing architecture.
