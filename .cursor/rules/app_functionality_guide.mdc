---
description: 
globs: 
alwaysApply: false
---
# Fitness App Functionality Guide

This rule outlines the complete functionality of the **fitness_app** - a comprehensive Flutter fitness tracking application that helps users plan, track, and analyze their workouts.

## 🎯 Core Features

### 1. Authentication & User Management
- **Sign Up/Sign In**: Secure user registration and login using Supabase authentication
  - Email/password authentication via [lib/features/auth](mdc:lib/features/auth)
  - User session management and auto-login
  - Password validation and account verification

- **User Profiles**: Comprehensive user profile system via [lib/features/profile](mdc:lib/features/profile)
  - Personal information (age, gender, height, weight)
  - BMI calculation and health metrics
  - Profile completion tracking

### 2. Onboarding Experience
- **Guided Setup**: Multi-step onboarding process via [lib/features/onboarding](mdc:lib/features/onboarding)
  - Personal information collection
  - Fitness goals selection (weight loss, muscle gain, endurance, etc.)
  - Fitness level assessment (cardio, weightlifting)
  - Workout preferences (frequency, duration, environment)
  - Health conditions and dietary restrictions
  - Equipment availability assessment

### 3. Workout Management
- **Single Workout System**: **The app provides only ONE workout at a time** via [lib/features/dashboard](mdc:lib/features/dashboard)
  - AI-generated personalized workouts tailored to user profile
  - Single active workout prevents decision paralysis
  - Estimated duration and calorie burn
  - Exercise breakdown with sets, reps, and weights
  - **Next workout only becomes available after current workout completion**

- **n8n Workflow Integration**: Automated workout generation pipeline
  - **Workout completion triggers n8n flow** to generate next workout
  - AI analyzes previous workout performance
  - Considers user progress and adaptation
  - Ensures continuous progression without gaps
  - **Users cannot access new workouts until current one is finished**

- **Custom Workout Creation**: Build custom workouts via [lib/features/workout](mdc:lib/features/workout)
  - Exercise selection from comprehensive database
  - Configurable sets, reps, weights, and rest intervals
  - Exercise reordering and modification
  - Save custom workouts for future use

- **Workout Execution**: Real-time workout tracking
  - Exercise-by-exercise progression
  - Set completion with performance tracking
  - Rest timer between sets
  - Performance feedback (difficulty rating)
  - Personal record (PR) detection and celebration

### 4. Exercise Database
- **Comprehensive Library**: Extensive exercise database via [lib/features/workout/domain/providers/exercise_selection_provider.dart](mdc:lib/features/workout/domain/providers/exercise_selection_provider.dart)
  - Exercises categorized by muscle groups
  - Equipment-based filtering
  - Exercise descriptions and instructions
  - Video demonstrations and thumbnails
  - Primary muscle targeting information

### 5. Progress Tracking & Analytics
- **Performance Metrics**: Detailed workout analytics via [lib/features/statistics](mdc:lib/features/statistics)
  - Workout completion rates
  - Personal record tracking
  - Weight progression over time
  - Volume and intensity metrics

- **Session Data**: Comprehensive workout logging
  - Completed sets with actual reps and weights
  - Performance difficulty ratings
  - Duration tracking
  - Calorie estimates

### 6. Dashboard & Home Experience
- **Activity Overview**: Main dashboard via [lib/features/dashboard](mdc:lib/features/dashboard)
  - **Single workout display** - only current workout shown
  - Weekly activity charts
  - Progress indicators
  - Motivational messaging
  - Quick workout access
  - **No workout queue or selection - one active workout only**

## 🔧 Technical Features

### Data Management
- **Supabase Integration**: Backend powered by Supabase
  - Real-time data synchronization
  - Secure user authentication
  - Scalable workout and exercise storage
  - Progress tracking across devices

- **n8n Automation**: External workflow automation
  - **Triggered on workout completion**
  - Processes user performance data
  - Generates next personalized workout
  - **Ensures single-workout availability model**

### User Experience
- **Dark Theme**: Modern dark UI with orange accent colors
  - High contrast ratios for accessibility
  - Smooth animations and transitions
  - Glass-morphism design elements
  - Haptic feedback for interactions

- **Accessibility**: WCAG AAA compliant
  - High contrast mode support
  - Reduced motion preferences
  - Screen reader compatibility
  - Minimum touch target compliance

### Performance
- **Optimized Rendering**: Efficient workout tracking
  - Lazy loading for exercise lists
  - Video preloading for workout preparation
  - Auto-save during workout sessions
  - Offline capability for workout execution

## 🏋️ Workout Flow

**⚠️ CRITICAL: The app follows a strict ONE-WORKOUT-AT-A-TIME model**

1. **Workout Discovery**: User sees **single** personalized workout on dashboard
   - Only one workout available at any time
   - Cannot skip or access alternative workouts
2. **Workout Preparation**: Exercise preview with estimated duration/calories
3. **Workout Execution**: 
   - Exercise-by-exercise progression
   - Set completion with weight/rep tracking
   - Rest timers between sets
   - Performance feedback collection
4. **Workout Completion**: 
   - Summary of performance
   - PR achievements highlighted
   - Progress saved to database
   - **n8n workflow automatically triggered**
5. **Next Workout Generation**: 
   - n8n flow processes workout completion data
   - AI generates next personalized workout
   - New workout becomes available on dashboard
   - **Process repeats with new single workout**

## 📊 Data Structure

### Workout Models
- **WorkoutSession**: Complete workout data via [lib/features/workout/domain/models/workout_session.dart](mdc:lib/features/workout/domain/models/workout_session.dart)
- **WorkoutExercise**: Individual exercise configuration
- **TodayWorkout**: Daily workout recommendations via [lib/features/dashboard/domain/models/today_workout.dart](mdc:lib/features/dashboard/domain/models/today_workout.dart)

### User Data
- **UserProfile**: Comprehensive user information via [lib/features/profile/domain/models/user_profile.dart](mdc:lib/features/profile/domain/models/user_profile.dart)
- **OnboardingState**: Setup progress tracking

## 🎨 UI/UX Highlights

- **Modern Design**: Dark theme with orange gradient accents
- **Smooth Animations**: Spring-based animations throughout
- **Glass Morphism**: Frosted glass cards and overlays
- **Responsive**: Optimized for various screen sizes
- **Intuitive Navigation**: Bottom navigation with contextual screens

This app provides a complete fitness tracking experience from user onboarding through workout completion and progress analysis.

## 🔄 Key Architectural Decision: Single Workout Model

**The app deliberately restricts users to ONE workout at a time to:**
- Eliminate decision paralysis and choice overload
- Ensure users complete workouts rather than skipping around
- Allow AI to properly track progression and adaptation
- Maintain consistent training stimulus and recovery patterns
- Trigger automated n8n workflow for intelligent workout generation

**Users cannot:**
- Browse multiple available workouts
- Skip workouts without completing them
- Access workout libraries or catalogs
- Generate workouts manually on-demand

**The n8n integration ensures:**
- Seamless workout progression
- Data-driven workout adaptation
- Continuous personalization based on performance
- Zero gaps between workout completions and next workout availability
